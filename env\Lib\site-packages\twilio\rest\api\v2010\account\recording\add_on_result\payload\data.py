r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class DataInstance(InstanceResource):
    """
    :ivar redirect_to: The URL to redirect to to get the data returned by the AddOn that was previously stored.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        reference_sid: str,
        add_on_result_sid: str,
        payload_sid: str,
    ):
        super().__init__(version)

        self.redirect_to: Optional[str] = payload.get("redirect_to")

        self._solution = {
            "account_sid": account_sid,
            "reference_sid": reference_sid,
            "add_on_result_sid": add_on_result_sid,
            "payload_sid": payload_sid,
        }
        self._context: Optional[DataContext] = None

    @property
    def _proxy(self) -> "DataContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: DataContext for this DataInstance
        """
        if self._context is None:
            self._context = DataContext(
                self._version,
                account_sid=self._solution["account_sid"],
                reference_sid=self._solution["reference_sid"],
                add_on_result_sid=self._solution["add_on_result_sid"],
                payload_sid=self._solution["payload_sid"],
            )
        return self._context

    def fetch(self) -> "DataInstance":
        """
        Fetch the DataInstance


        :returns: The fetched DataInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "DataInstance":
        """
        Asynchronous coroutine to fetch the DataInstance


        :returns: The fetched DataInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.DataInstance {}>".format(context)


class DataContext(InstanceContext):

    def __init__(
        self,
        version: Version,
        account_sid: str,
        reference_sid: str,
        add_on_result_sid: str,
        payload_sid: str,
    ):
        """
        Initialize the DataContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Recording AddOnResult Payload resource to fetch.
        :param reference_sid: The SID of the recording to which the AddOnResult resource that contains the payload to fetch belongs.
        :param add_on_result_sid: The SID of the AddOnResult to which the payload to fetch belongs.
        :param payload_sid: The Twilio-provided string that uniquely identifies the Recording AddOnResult Payload resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "reference_sid": reference_sid,
            "add_on_result_sid": add_on_result_sid,
            "payload_sid": payload_sid,
        }
        self._uri = "/Accounts/{account_sid}/Recordings/{reference_sid}/AddOnResults/{add_on_result_sid}/Payloads/{payload_sid}/Data.json".format(
            **self._solution
        )

    def fetch(self) -> DataInstance:
        """
        Fetch the DataInstance


        :returns: The fetched DataInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return DataInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            payload_sid=self._solution["payload_sid"],
        )

    async def fetch_async(self) -> DataInstance:
        """
        Asynchronous coroutine to fetch the DataInstance


        :returns: The fetched DataInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return DataInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            payload_sid=self._solution["payload_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.DataContext {}>".format(context)


class DataList(ListResource):

    def __init__(
        self,
        version: Version,
        account_sid: str,
        reference_sid: str,
        add_on_result_sid: str,
        payload_sid: str,
    ):
        """
        Initialize the DataList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Recording AddOnResult Payload resource to fetch.
        :param reference_sid: The SID of the recording to which the AddOnResult resource that contains the payload to fetch belongs.
        :param add_on_result_sid: The SID of the AddOnResult to which the payload to fetch belongs.
        :param payload_sid: The Twilio-provided string that uniquely identifies the Recording AddOnResult Payload resource to fetch.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "reference_sid": reference_sid,
            "add_on_result_sid": add_on_result_sid,
            "payload_sid": payload_sid,
        }

    def get(self) -> DataContext:
        """
        Constructs a DataContext

        """
        return DataContext(
            self._version,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            payload_sid=self._solution["payload_sid"],
        )

    def __call__(self) -> DataContext:
        """
        Constructs a DataContext

        """
        return DataContext(
            self._version,
            account_sid=self._solution["account_sid"],
            reference_sid=self._solution["reference_sid"],
            add_on_result_sid=self._solution["add_on_result_sid"],
            payload_sid=self._solution["payload_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.DataList>"
