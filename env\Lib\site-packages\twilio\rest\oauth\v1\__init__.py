r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Oauth
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.oauth.v1.authorize import AuthorizeList
from twilio.rest.oauth.v1.token import TokenList


class V1(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V1 version of Oauth

        :param domain: The Twilio.oauth domain
        """
        super().__init__(domain, "v1")
        self._authorize: Optional[AuthorizeList] = None
        self._token: Optional[TokenList] = None

    @property
    def authorize(self) -> AuthorizeList:
        if self._authorize is None:
            self._authorize = AuthorizeList(self)
        return self._authorize

    @property
    def token(self) -> TokenList:
        if self._token is None:
            self._token = TokenList(self)
        return self._token

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Oauth.V1>"
