r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import date, datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.api.v2010.account.conference.participant import ParticipantList
from twilio.rest.api.v2010.account.conference.recording import RecordingList


class ConferenceInstance(InstanceResource):

    class ReasonConferenceEnded(object):
        CONFERENCE_ENDED_VIA_API = "conference-ended-via-api"
        PARTICIPANT_WITH_END_CONFERENCE_ON_EXIT_LEFT = (
            "participant-with-end-conference-on-exit-left"
        )
        PARTICIPANT_WITH_END_CONFERENCE_ON_EXIT_KICKED = (
            "participant-with-end-conference-on-exit-kicked"
        )
        LAST_PARTICIPANT_KICKED = "last-participant-kicked"
        LAST_PARTICIPANT_LEFT = "last-participant-left"

    class Status(object):
        INIT = "init"
        IN_PROGRESS = "in-progress"
        COMPLETED = "completed"

    class UpdateStatus(object):
        COMPLETED = "completed"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created this Conference resource.
    :ivar date_created: The date and time in UTC that this resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in UTC that this resource was last updated, specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar api_version: The API version used to create this conference.
    :ivar friendly_name: A string that you assigned to describe this conference room. Maximum length is 128 characters.
    :ivar region: A string that represents the Twilio Region where the conference audio was mixed. May be `us1`, `us2`, `ie1`,  `de1`, `sg1`, `br1`, `au1`, and `jp1`. Basic conference audio will always be mixed in `us1`. Global Conference audio will be mixed nearest to the majority of participants.
    :ivar sid: The unique, Twilio-provided string used to identify this Conference resource.
    :ivar status: 
    :ivar uri: The URI of this resource, relative to `https://api.twilio.com`.
    :ivar subresource_uris: A list of related resources identified by their URIs relative to `https://api.twilio.com`.
    :ivar reason_conference_ended: 
    :ivar call_sid_ending_conference: The call SID that caused the conference to end.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.api_version: Optional[str] = payload.get("api_version")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.region: Optional[str] = payload.get("region")
        self.sid: Optional[str] = payload.get("sid")
        self.status: Optional["ConferenceInstance.Status"] = payload.get("status")
        self.uri: Optional[str] = payload.get("uri")
        self.subresource_uris: Optional[Dict[str, object]] = payload.get(
            "subresource_uris"
        )
        self.reason_conference_ended: Optional[
            "ConferenceInstance.ReasonConferenceEnded"
        ] = payload.get("reason_conference_ended")
        self.call_sid_ending_conference: Optional[str] = payload.get(
            "call_sid_ending_conference"
        )

        self._solution = {
            "account_sid": account_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[ConferenceContext] = None

    @property
    def _proxy(self) -> "ConferenceContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ConferenceContext for this ConferenceInstance
        """
        if self._context is None:
            self._context = ConferenceContext(
                self._version,
                account_sid=self._solution["account_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "ConferenceInstance":
        """
        Fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ConferenceInstance":
        """
        Asynchronous coroutine to fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        status: Union["ConferenceInstance.UpdateStatus", object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
    ) -> "ConferenceInstance":
        """
        Update the ConferenceInstance

        :param status:
        :param announce_url: The URL we should call to announce something into the conference. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method used to call `announce_url`. Can be: `GET` or `POST` and the default is `POST`

        :returns: The updated ConferenceInstance
        """
        return self._proxy.update(
            status=status,
            announce_url=announce_url,
            announce_method=announce_method,
        )

    async def update_async(
        self,
        status: Union["ConferenceInstance.UpdateStatus", object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
    ) -> "ConferenceInstance":
        """
        Asynchronous coroutine to update the ConferenceInstance

        :param status:
        :param announce_url: The URL we should call to announce something into the conference. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method used to call `announce_url`. Can be: `GET` or `POST` and the default is `POST`

        :returns: The updated ConferenceInstance
        """
        return await self._proxy.update_async(
            status=status,
            announce_url=announce_url,
            announce_method=announce_method,
        )

    @property
    def participants(self) -> ParticipantList:
        """
        Access the participants
        """
        return self._proxy.participants

    @property
    def recordings(self) -> RecordingList:
        """
        Access the recordings
        """
        return self._proxy.recordings

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.ConferenceInstance {}>".format(context)


class ConferenceContext(InstanceContext):

    def __init__(self, version: Version, account_sid: str, sid: str):
        """
        Initialize the ConferenceContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Conference resource(s) to update.
        :param sid: The Twilio-provided string that uniquely identifies the Conference resource to update
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "sid": sid,
        }
        self._uri = "/Accounts/{account_sid}/Conferences/{sid}.json".format(
            **self._solution
        )

        self._participants: Optional[ParticipantList] = None
        self._recordings: Optional[RecordingList] = None

    def fetch(self) -> ConferenceInstance:
        """
        Fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ConferenceInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ConferenceInstance:
        """
        Asynchronous coroutine to fetch the ConferenceInstance


        :returns: The fetched ConferenceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ConferenceInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        status: Union["ConferenceInstance.UpdateStatus", object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
    ) -> ConferenceInstance:
        """
        Update the ConferenceInstance

        :param status:
        :param announce_url: The URL we should call to announce something into the conference. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method used to call `announce_url`. Can be: `GET` or `POST` and the default is `POST`

        :returns: The updated ConferenceInstance
        """

        data = values.of(
            {
                "Status": status,
                "AnnounceUrl": announce_url,
                "AnnounceMethod": announce_method,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConferenceInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        status: Union["ConferenceInstance.UpdateStatus", object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
    ) -> ConferenceInstance:
        """
        Asynchronous coroutine to update the ConferenceInstance

        :param status:
        :param announce_url: The URL we should call to announce something into the conference. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method used to call `announce_url`. Can be: `GET` or `POST` and the default is `POST`

        :returns: The updated ConferenceInstance
        """

        data = values.of(
            {
                "Status": status,
                "AnnounceUrl": announce_url,
                "AnnounceMethod": announce_method,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConferenceInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            sid=self._solution["sid"],
        )

    @property
    def participants(self) -> ParticipantList:
        """
        Access the participants
        """
        if self._participants is None:
            self._participants = ParticipantList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._participants

    @property
    def recordings(self) -> RecordingList:
        """
        Access the recordings
        """
        if self._recordings is None:
            self._recordings = RecordingList(
                self._version,
                self._solution["account_sid"],
                self._solution["sid"],
            )
        return self._recordings

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.ConferenceContext {}>".format(context)


class ConferencePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ConferenceInstance:
        """
        Build an instance of ConferenceInstance

        :param payload: Payload response from the API
        """
        return ConferenceInstance(
            self._version, payload, account_sid=self._solution["account_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ConferencePage>"


class ConferenceList(ListResource):

    def __init__(self, version: Version, account_sid: str):
        """
        Initialize the ConferenceList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Conference resource(s) to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
        }
        self._uri = "/Accounts/{account_sid}/Conferences.json".format(**self._solution)

    def stream(
        self,
        date_created: Union[date, object] = values.unset,
        date_created_before: Union[date, object] = values.unset,
        date_created_after: Union[date, object] = values.unset,
        date_updated: Union[date, object] = values.unset,
        date_updated_before: Union[date, object] = values.unset,
        date_updated_after: Union[date, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union["ConferenceInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ConferenceInstance]:
        """
        Streams ConferenceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param date date_created: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_before: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_after: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_updated: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_before: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_after: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param str friendly_name: The string that identifies the Conference resources to read.
        :param &quot;ConferenceInstance.Status&quot; status: The status of the resources to read. Can be: `init`, `in-progress`, or `completed`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            date_created=date_created,
            date_created_before=date_created_before,
            date_created_after=date_created_after,
            date_updated=date_updated,
            date_updated_before=date_updated_before,
            date_updated_after=date_updated_after,
            friendly_name=friendly_name,
            status=status,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        date_created: Union[date, object] = values.unset,
        date_created_before: Union[date, object] = values.unset,
        date_created_after: Union[date, object] = values.unset,
        date_updated: Union[date, object] = values.unset,
        date_updated_before: Union[date, object] = values.unset,
        date_updated_after: Union[date, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union["ConferenceInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ConferenceInstance]:
        """
        Asynchronously streams ConferenceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param date date_created: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_before: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_after: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_updated: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_before: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_after: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param str friendly_name: The string that identifies the Conference resources to read.
        :param &quot;ConferenceInstance.Status&quot; status: The status of the resources to read. Can be: `init`, `in-progress`, or `completed`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            date_created=date_created,
            date_created_before=date_created_before,
            date_created_after=date_created_after,
            date_updated=date_updated,
            date_updated_before=date_updated_before,
            date_updated_after=date_updated_after,
            friendly_name=friendly_name,
            status=status,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        date_created: Union[date, object] = values.unset,
        date_created_before: Union[date, object] = values.unset,
        date_created_after: Union[date, object] = values.unset,
        date_updated: Union[date, object] = values.unset,
        date_updated_before: Union[date, object] = values.unset,
        date_updated_after: Union[date, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union["ConferenceInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConferenceInstance]:
        """
        Lists ConferenceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param date date_created: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_before: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_after: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_updated: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_before: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_after: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param str friendly_name: The string that identifies the Conference resources to read.
        :param &quot;ConferenceInstance.Status&quot; status: The status of the resources to read. Can be: `init`, `in-progress`, or `completed`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                date_created=date_created,
                date_created_before=date_created_before,
                date_created_after=date_created_after,
                date_updated=date_updated,
                date_updated_before=date_updated_before,
                date_updated_after=date_updated_after,
                friendly_name=friendly_name,
                status=status,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        date_created: Union[date, object] = values.unset,
        date_created_before: Union[date, object] = values.unset,
        date_created_after: Union[date, object] = values.unset,
        date_updated: Union[date, object] = values.unset,
        date_updated_before: Union[date, object] = values.unset,
        date_updated_after: Union[date, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union["ConferenceInstance.Status", object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConferenceInstance]:
        """
        Asynchronously lists ConferenceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param date date_created: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_before: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_created_after: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date date_updated: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_before: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date date_updated_after: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param str friendly_name: The string that identifies the Conference resources to read.
        :param &quot;ConferenceInstance.Status&quot; status: The status of the resources to read. Can be: `init`, `in-progress`, or `completed`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                date_created=date_created,
                date_created_before=date_created_before,
                date_created_after=date_created_after,
                date_updated=date_updated,
                date_updated_before=date_updated_before,
                date_updated_after=date_updated_after,
                friendly_name=friendly_name,
                status=status,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        date_created: Union[date, object] = values.unset,
        date_created_before: Union[date, object] = values.unset,
        date_created_after: Union[date, object] = values.unset,
        date_updated: Union[date, object] = values.unset,
        date_updated_before: Union[date, object] = values.unset,
        date_updated_after: Union[date, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union["ConferenceInstance.Status", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConferencePage:
        """
        Retrieve a single page of ConferenceInstance records from the API.
        Request is executed immediately

        :param date_created: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date_created_before: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date_created_after: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date_updated: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date_updated_before: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date_updated_after: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param friendly_name: The string that identifies the Conference resources to read.
        :param status: The status of the resources to read. Can be: `init`, `in-progress`, or `completed`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConferenceInstance
        """
        data = values.of(
            {
                "DateCreated": serialize.iso8601_date(date_created),
                "DateCreated<": serialize.iso8601_date(date_created_before),
                "DateCreated>": serialize.iso8601_date(date_created_after),
                "DateUpdated": serialize.iso8601_date(date_updated),
                "DateUpdated<": serialize.iso8601_date(date_updated_before),
                "DateUpdated>": serialize.iso8601_date(date_updated_after),
                "FriendlyName": friendly_name,
                "Status": status,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConferencePage(self._version, response, self._solution)

    async def page_async(
        self,
        date_created: Union[date, object] = values.unset,
        date_created_before: Union[date, object] = values.unset,
        date_created_after: Union[date, object] = values.unset,
        date_updated: Union[date, object] = values.unset,
        date_updated_before: Union[date, object] = values.unset,
        date_updated_after: Union[date, object] = values.unset,
        friendly_name: Union[str, object] = values.unset,
        status: Union["ConferenceInstance.Status", object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConferencePage:
        """
        Asynchronously retrieve a single page of ConferenceInstance records from the API.
        Request is executed immediately

        :param date_created: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date_created_before: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date_created_after: Only include conferences that were created on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were created on this date. You can also specify an inequality, such as `DateCreated<=YYYY-MM-DD`, to read conferences that were created on or before midnight of this date, and `DateCreated>=YYYY-MM-DD` to read conferences that were created on or after midnight of this date.
        :param date_updated: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date_updated_before: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param date_updated_after: Only include conferences that were last updated on this date. Specify a date as `YYYY-MM-DD` in UTC, for example: `2009-07-06`, to read only conferences that were last updated on this date. You can also specify an inequality, such as `DateUpdated<=YYYY-MM-DD`, to read conferences that were last updated on or before midnight of this date, and `DateUpdated>=YYYY-MM-DD` to read conferences that were last updated on or after midnight of this date.
        :param friendly_name: The string that identifies the Conference resources to read.
        :param status: The status of the resources to read. Can be: `init`, `in-progress`, or `completed`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConferenceInstance
        """
        data = values.of(
            {
                "DateCreated": serialize.iso8601_date(date_created),
                "DateCreated<": serialize.iso8601_date(date_created_before),
                "DateCreated>": serialize.iso8601_date(date_created_after),
                "DateUpdated": serialize.iso8601_date(date_updated),
                "DateUpdated<": serialize.iso8601_date(date_updated_before),
                "DateUpdated>": serialize.iso8601_date(date_updated_after),
                "FriendlyName": friendly_name,
                "Status": status,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConferencePage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ConferencePage:
        """
        Retrieve a specific page of ConferenceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConferenceInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ConferencePage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ConferencePage:
        """
        Asynchronously retrieve a specific page of ConferenceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConferenceInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ConferencePage(self._version, response, self._solution)

    def get(self, sid: str) -> ConferenceContext:
        """
        Constructs a ConferenceContext

        :param sid: The Twilio-provided string that uniquely identifies the Conference resource to update
        """
        return ConferenceContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __call__(self, sid: str) -> ConferenceContext:
        """
        Constructs a ConferenceContext

        :param sid: The Twilio-provided string that uniquely identifies the Conference resource to update
        """
        return ConferenceContext(
            self._version, account_sid=self._solution["account_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ConferenceList>"
