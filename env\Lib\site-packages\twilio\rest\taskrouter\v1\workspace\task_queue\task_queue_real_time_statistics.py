r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class TaskQueueRealTimeStatisticsInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the TaskQueue resource.
    :ivar activity_statistics: The number of current Workers by Activity.
    :ivar longest_task_waiting_age: The age of the longest waiting Task.
    :ivar longest_task_waiting_sid: The SID of the longest waiting Task.
    :ivar longest_relative_task_age_in_queue: The relative age in the TaskQueue for the longest waiting Task. Calculation is based on the time when the Task entered the TaskQueue.
    :ivar longest_relative_task_sid_in_queue: The Task SID of the Task waiting in the TaskQueue the longest. Calculation is based on the time when the Task entered the TaskQueue.
    :ivar task_queue_sid: The SID of the TaskQueue from which these statistics were calculated.
    :ivar tasks_by_priority: The number of Tasks by priority. For example: `{\"0\": \"10\", \"99\": \"5\"}` shows 10 Tasks at priority 0 and 5 at priority 99.
    :ivar tasks_by_status: The number of Tasks by their current status. For example: `{\"pending\": \"1\", \"reserved\": \"3\", \"assigned\": \"2\", \"completed\": \"5\"}`.
    :ivar total_available_workers: The total number of Workers in the TaskQueue with an `available` status. Workers with an `available` status may already have active interactions or may have none.
    :ivar total_eligible_workers: The total number of Workers eligible for Tasks in the TaskQueue, independent of their Activity state.
    :ivar total_tasks: The total number of Tasks.
    :ivar workspace_sid: The SID of the Workspace that contains the TaskQueue.
    :ivar url: The absolute URL of the TaskQueue statistics resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        workspace_sid: str,
        task_queue_sid: str,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.activity_statistics: Optional[List[Dict[str, object]]] = payload.get(
            "activity_statistics"
        )
        self.longest_task_waiting_age: Optional[int] = deserialize.integer(
            payload.get("longest_task_waiting_age")
        )
        self.longest_task_waiting_sid: Optional[str] = payload.get(
            "longest_task_waiting_sid"
        )
        self.longest_relative_task_age_in_queue: Optional[int] = deserialize.integer(
            payload.get("longest_relative_task_age_in_queue")
        )
        self.longest_relative_task_sid_in_queue: Optional[str] = payload.get(
            "longest_relative_task_sid_in_queue"
        )
        self.task_queue_sid: Optional[str] = payload.get("task_queue_sid")
        self.tasks_by_priority: Optional[Dict[str, object]] = payload.get(
            "tasks_by_priority"
        )
        self.tasks_by_status: Optional[Dict[str, object]] = payload.get(
            "tasks_by_status"
        )
        self.total_available_workers: Optional[int] = deserialize.integer(
            payload.get("total_available_workers")
        )
        self.total_eligible_workers: Optional[int] = deserialize.integer(
            payload.get("total_eligible_workers")
        )
        self.total_tasks: Optional[int] = deserialize.integer(
            payload.get("total_tasks")
        )
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "workspace_sid": workspace_sid,
            "task_queue_sid": task_queue_sid,
        }
        self._context: Optional[TaskQueueRealTimeStatisticsContext] = None

    @property
    def _proxy(self) -> "TaskQueueRealTimeStatisticsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: TaskQueueRealTimeStatisticsContext for this TaskQueueRealTimeStatisticsInstance
        """
        if self._context is None:
            self._context = TaskQueueRealTimeStatisticsContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
                task_queue_sid=self._solution["task_queue_sid"],
            )
        return self._context

    def fetch(
        self, task_channel: Union[str, object] = values.unset
    ) -> "TaskQueueRealTimeStatisticsInstance":
        """
        Fetch the TaskQueueRealTimeStatisticsInstance

        :param task_channel: The TaskChannel for which to fetch statistics. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched TaskQueueRealTimeStatisticsInstance
        """
        return self._proxy.fetch(
            task_channel=task_channel,
        )

    async def fetch_async(
        self, task_channel: Union[str, object] = values.unset
    ) -> "TaskQueueRealTimeStatisticsInstance":
        """
        Asynchronous coroutine to fetch the TaskQueueRealTimeStatisticsInstance

        :param task_channel: The TaskChannel for which to fetch statistics. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched TaskQueueRealTimeStatisticsInstance
        """
        return await self._proxy.fetch_async(
            task_channel=task_channel,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.TaskQueueRealTimeStatisticsInstance {}>".format(
            context
        )


class TaskQueueRealTimeStatisticsContext(InstanceContext):

    def __init__(self, version: Version, workspace_sid: str, task_queue_sid: str):
        """
        Initialize the TaskQueueRealTimeStatisticsContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the TaskQueue to fetch.
        :param task_queue_sid: The SID of the TaskQueue for which to fetch statistics.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "task_queue_sid": task_queue_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/TaskQueues/{task_queue_sid}/RealTimeStatistics".format(
            **self._solution
        )

    def fetch(
        self, task_channel: Union[str, object] = values.unset
    ) -> TaskQueueRealTimeStatisticsInstance:
        """
        Fetch the TaskQueueRealTimeStatisticsInstance

        :param task_channel: The TaskChannel for which to fetch statistics. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched TaskQueueRealTimeStatisticsInstance
        """

        data = values.of(
            {
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return TaskQueueRealTimeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            task_queue_sid=self._solution["task_queue_sid"],
        )

    async def fetch_async(
        self, task_channel: Union[str, object] = values.unset
    ) -> TaskQueueRealTimeStatisticsInstance:
        """
        Asynchronous coroutine to fetch the TaskQueueRealTimeStatisticsInstance

        :param task_channel: The TaskChannel for which to fetch statistics. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched TaskQueueRealTimeStatisticsInstance
        """

        data = values.of(
            {
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return TaskQueueRealTimeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            task_queue_sid=self._solution["task_queue_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.TaskQueueRealTimeStatisticsContext {}>".format(
            context
        )


class TaskQueueRealTimeStatisticsList(ListResource):

    def __init__(self, version: Version, workspace_sid: str, task_queue_sid: str):
        """
        Initialize the TaskQueueRealTimeStatisticsList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the TaskQueue to fetch.
        :param task_queue_sid: The SID of the TaskQueue for which to fetch statistics.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "task_queue_sid": task_queue_sid,
        }

    def get(self) -> TaskQueueRealTimeStatisticsContext:
        """
        Constructs a TaskQueueRealTimeStatisticsContext

        """
        return TaskQueueRealTimeStatisticsContext(
            self._version,
            workspace_sid=self._solution["workspace_sid"],
            task_queue_sid=self._solution["task_queue_sid"],
        )

    def __call__(self) -> TaskQueueRealTimeStatisticsContext:
        """
        Constructs a TaskQueueRealTimeStatisticsContext

        """
        return TaskQueueRealTimeStatisticsContext(
            self._version,
            workspace_sid=self._solution["workspace_sid"],
            task_queue_sid=self._solution["task_queue_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.TaskQueueRealTimeStatisticsList>"
