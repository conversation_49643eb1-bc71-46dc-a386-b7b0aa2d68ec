r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Serverless
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class LogInstance(InstanceResource):

    class Level(object):
        INFO = "info"
        WARN = "warn"
        ERROR = "error"

    """
    :ivar sid: The unique string that we created to identify the Log resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Log resource.
    :ivar service_sid: The SID of the Service that the Log resource is associated with.
    :ivar environment_sid: The SID of the environment in which the log occurred.
    :ivar build_sid: The SID of the build that corresponds to the log.
    :ivar deployment_sid: The SID of the deployment that corresponds to the log.
    :ivar function_sid: The SID of the function whose invocation produced the log.
    :ivar request_sid: The SID of the request associated with the log.
    :ivar level: 
    :ivar message: The log message.
    :ivar date_created: The date and time in GMT when the Log resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Log resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        service_sid: str,
        environment_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.service_sid: Optional[str] = payload.get("service_sid")
        self.environment_sid: Optional[str] = payload.get("environment_sid")
        self.build_sid: Optional[str] = payload.get("build_sid")
        self.deployment_sid: Optional[str] = payload.get("deployment_sid")
        self.function_sid: Optional[str] = payload.get("function_sid")
        self.request_sid: Optional[str] = payload.get("request_sid")
        self.level: Optional["LogInstance.Level"] = payload.get("level")
        self.message: Optional[str] = payload.get("message")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "service_sid": service_sid,
            "environment_sid": environment_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[LogContext] = None

    @property
    def _proxy(self) -> "LogContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: LogContext for this LogInstance
        """
        if self._context is None:
            self._context = LogContext(
                self._version,
                service_sid=self._solution["service_sid"],
                environment_sid=self._solution["environment_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "LogInstance":
        """
        Fetch the LogInstance


        :returns: The fetched LogInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "LogInstance":
        """
        Asynchronous coroutine to fetch the LogInstance


        :returns: The fetched LogInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Serverless.V1.LogInstance {}>".format(context)


class LogContext(InstanceContext):

    def __init__(
        self, version: Version, service_sid: str, environment_sid: str, sid: str
    ):
        """
        Initialize the LogContext

        :param version: Version that contains the resource
        :param service_sid: The SID of the Service to fetch the Log resource from.
        :param environment_sid: The SID of the environment with the Log resource to fetch.
        :param sid: The SID of the Log resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "environment_sid": environment_sid,
            "sid": sid,
        }
        self._uri = (
            "/Services/{service_sid}/Environments/{environment_sid}/Logs/{sid}".format(
                **self._solution
            )
        )

    def fetch(self) -> LogInstance:
        """
        Fetch the LogInstance


        :returns: The fetched LogInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return LogInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> LogInstance:
        """
        Asynchronous coroutine to fetch the LogInstance


        :returns: The fetched LogInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return LogInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Serverless.V1.LogContext {}>".format(context)


class LogPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> LogInstance:
        """
        Build an instance of LogInstance

        :param payload: Payload response from the API
        """
        return LogInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Serverless.V1.LogPage>"


class LogList(ListResource):

    def __init__(self, version: Version, service_sid: str, environment_sid: str):
        """
        Initialize the LogList

        :param version: Version that contains the resource
        :param service_sid: The SID of the Service to read the Log resource from.
        :param environment_sid: The SID of the environment with the Log resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "environment_sid": environment_sid,
        }
        self._uri = (
            "/Services/{service_sid}/Environments/{environment_sid}/Logs".format(
                **self._solution
            )
        )

    def stream(
        self,
        function_sid: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[LogInstance]:
        """
        Streams LogInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str function_sid: The SID of the function whose invocation produced the Log resources to read.
        :param datetime start_date: The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
        :param datetime end_date: The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            function_sid=function_sid,
            start_date=start_date,
            end_date=end_date,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        function_sid: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[LogInstance]:
        """
        Asynchronously streams LogInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str function_sid: The SID of the function whose invocation produced the Log resources to read.
        :param datetime start_date: The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
        :param datetime end_date: The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            function_sid=function_sid,
            start_date=start_date,
            end_date=end_date,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        function_sid: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[LogInstance]:
        """
        Lists LogInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str function_sid: The SID of the function whose invocation produced the Log resources to read.
        :param datetime start_date: The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
        :param datetime end_date: The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                function_sid=function_sid,
                start_date=start_date,
                end_date=end_date,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        function_sid: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[LogInstance]:
        """
        Asynchronously lists LogInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str function_sid: The SID of the function whose invocation produced the Log resources to read.
        :param datetime start_date: The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
        :param datetime end_date: The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                function_sid=function_sid,
                start_date=start_date,
                end_date=end_date,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        function_sid: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> LogPage:
        """
        Retrieve a single page of LogInstance records from the API.
        Request is executed immediately

        :param function_sid: The SID of the function whose invocation produced the Log resources to read.
        :param start_date: The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
        :param end_date: The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of LogInstance
        """
        data = values.of(
            {
                "FunctionSid": function_sid,
                "StartDate": serialize.iso8601_datetime(start_date),
                "EndDate": serialize.iso8601_datetime(end_date),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return LogPage(self._version, response, self._solution)

    async def page_async(
        self,
        function_sid: Union[str, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        end_date: Union[datetime, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> LogPage:
        """
        Asynchronously retrieve a single page of LogInstance records from the API.
        Request is executed immediately

        :param function_sid: The SID of the function whose invocation produced the Log resources to read.
        :param start_date: The date/time (in GMT, ISO 8601) after which the Log resources must have been created. Defaults to 1 day prior to current date/time.
        :param end_date: The date/time (in GMT, ISO 8601) before which the Log resources must have been created. Defaults to current date/time.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of LogInstance
        """
        data = values.of(
            {
                "FunctionSid": function_sid,
                "StartDate": serialize.iso8601_datetime(start_date),
                "EndDate": serialize.iso8601_datetime(end_date),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return LogPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> LogPage:
        """
        Retrieve a specific page of LogInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of LogInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return LogPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> LogPage:
        """
        Asynchronously retrieve a specific page of LogInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of LogInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return LogPage(self._version, response, self._solution)

    def get(self, sid: str) -> LogContext:
        """
        Constructs a LogContext

        :param sid: The SID of the Log resource to fetch.
        """
        return LogContext(
            self._version,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> LogContext:
        """
        Constructs a LogContext

        :param sid: The SID of the Log resource to fetch.
        """
        return LogContext(
            self._version,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Serverless.V1.LogList>"
