r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from twilio.base import values
from twilio.base.instance_context import InstanceContext

from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class PortingWebhookConfigurationDeleteContext(InstanceContext):

    def __init__(
        self,
        version: Version,
        webhook_type: "PortingWebhookConfigurationDeleteInstance.WebhookType",
    ):
        """
        Initialize the PortingWebhookConfigurationDeleteContext

        :param version: Version that contains the resource
        :param webhook_type: The webhook type for the configuration to be delete. `PORT_IN`, `PORT_OUT`
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "webhook_type": webhook_type,
        }
        self._uri = "/Porting/Configuration/Webhook/{webhook_type}".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the PortingWebhookConfigurationDeleteInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the PortingWebhookConfigurationDeleteInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V1.PortingWebhookConfigurationDeleteContext {}>".format(
            context
        )


class PortingWebhookConfigurationDeleteList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the PortingWebhookConfigurationDeleteList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(
        self, webhook_type: "PortingWebhookConfigurationDeleteInstance.WebhookType"
    ) -> PortingWebhookConfigurationDeleteContext:
        """
        Constructs a PortingWebhookConfigurationDeleteContext

        :param webhook_type: The webhook type for the configuration to be delete. `PORT_IN`, `PORT_OUT`
        """
        return PortingWebhookConfigurationDeleteContext(
            self._version, webhook_type=webhook_type
        )

    def __call__(
        self, webhook_type: "PortingWebhookConfigurationDeleteInstance.WebhookType"
    ) -> PortingWebhookConfigurationDeleteContext:
        """
        Constructs a PortingWebhookConfigurationDeleteContext

        :param webhook_type: The webhook type for the configuration to be delete. `PORT_IN`, `PORT_OUT`
        """
        return PortingWebhookConfigurationDeleteContext(
            self._version, webhook_type=webhook_type
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V1.PortingWebhookConfigurationDeleteList>"
