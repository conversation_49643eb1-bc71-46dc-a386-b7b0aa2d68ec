r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class WorkersCumulativeStatisticsInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Worker resource.
    :ivar start_time: The beginning of the interval during which these statistics were calculated, in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar end_time: The end of the interval during which these statistics were calculated, in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar activity_durations: The minimum, average, maximum, and total time (in seconds) that Workers spent in each Activity.
    :ivar reservations_created: The total number of Reservations that were created.
    :ivar reservations_accepted: The total number of Reservations that were accepted.
    :ivar reservations_rejected: The total number of Reservations that were rejected.
    :ivar reservations_timed_out: The total number of Reservations that were timed out.
    :ivar reservations_canceled: The total number of Reservations that were canceled.
    :ivar reservations_rescinded: The total number of Reservations that were rescinded.
    :ivar workspace_sid: The SID of the Workspace that contains the Workers.
    :ivar url: The absolute URL of the Workers statistics resource.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], workspace_sid: str):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.start_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("start_time")
        )
        self.end_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("end_time")
        )
        self.activity_durations: Optional[List[Dict[str, object]]] = payload.get(
            "activity_durations"
        )
        self.reservations_created: Optional[int] = deserialize.integer(
            payload.get("reservations_created")
        )
        self.reservations_accepted: Optional[int] = deserialize.integer(
            payload.get("reservations_accepted")
        )
        self.reservations_rejected: Optional[int] = deserialize.integer(
            payload.get("reservations_rejected")
        )
        self.reservations_timed_out: Optional[int] = deserialize.integer(
            payload.get("reservations_timed_out")
        )
        self.reservations_canceled: Optional[int] = deserialize.integer(
            payload.get("reservations_canceled")
        )
        self.reservations_rescinded: Optional[int] = deserialize.integer(
            payload.get("reservations_rescinded")
        )
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._context: Optional[WorkersCumulativeStatisticsContext] = None

    @property
    def _proxy(self) -> "WorkersCumulativeStatisticsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: WorkersCumulativeStatisticsContext for this WorkersCumulativeStatisticsInstance
        """
        if self._context is None:
            self._context = WorkersCumulativeStatisticsContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
            )
        return self._context

    def fetch(
        self,
        end_date: Union[datetime, object] = values.unset,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> "WorkersCumulativeStatisticsInstance":
        """
        Fetch the WorkersCumulativeStatisticsInstance

        :param end_date: Only calculate statistics from this date and time and earlier, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param task_channel: Only calculate cumulative statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersCumulativeStatisticsInstance
        """
        return self._proxy.fetch(
            end_date=end_date,
            minutes=minutes,
            start_date=start_date,
            task_channel=task_channel,
        )

    async def fetch_async(
        self,
        end_date: Union[datetime, object] = values.unset,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> "WorkersCumulativeStatisticsInstance":
        """
        Asynchronous coroutine to fetch the WorkersCumulativeStatisticsInstance

        :param end_date: Only calculate statistics from this date and time and earlier, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param task_channel: Only calculate cumulative statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersCumulativeStatisticsInstance
        """
        return await self._proxy.fetch_async(
            end_date=end_date,
            minutes=minutes,
            start_date=start_date,
            task_channel=task_channel,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkersCumulativeStatisticsInstance {}>".format(
            context
        )


class WorkersCumulativeStatisticsContext(InstanceContext):

    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkersCumulativeStatisticsContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the resource to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Workers/CumulativeStatistics".format(
            **self._solution
        )

    def fetch(
        self,
        end_date: Union[datetime, object] = values.unset,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> WorkersCumulativeStatisticsInstance:
        """
        Fetch the WorkersCumulativeStatisticsInstance

        :param end_date: Only calculate statistics from this date and time and earlier, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param task_channel: Only calculate cumulative statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersCumulativeStatisticsInstance
        """

        data = values.of(
            {
                "EndDate": serialize.iso8601_datetime(end_date),
                "Minutes": minutes,
                "StartDate": serialize.iso8601_datetime(start_date),
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return WorkersCumulativeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
        )

    async def fetch_async(
        self,
        end_date: Union[datetime, object] = values.unset,
        minutes: Union[int, object] = values.unset,
        start_date: Union[datetime, object] = values.unset,
        task_channel: Union[str, object] = values.unset,
    ) -> WorkersCumulativeStatisticsInstance:
        """
        Asynchronous coroutine to fetch the WorkersCumulativeStatisticsInstance

        :param end_date: Only calculate statistics from this date and time and earlier, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param minutes: Only calculate statistics since this many minutes in the past. The default 15 minutes. This is helpful for displaying statistics for the last 15 minutes, 240 minutes (4 hours), and 480 minutes (8 hours) to see trends.
        :param start_date: Only calculate statistics from this date and time and later, specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
        :param task_channel: Only calculate cumulative statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkersCumulativeStatisticsInstance
        """

        data = values.of(
            {
                "EndDate": serialize.iso8601_datetime(end_date),
                "Minutes": minutes,
                "StartDate": serialize.iso8601_datetime(start_date),
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return WorkersCumulativeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkersCumulativeStatisticsContext {}>".format(
            context
        )


class WorkersCumulativeStatisticsList(ListResource):

    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkersCumulativeStatisticsList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the resource to fetch.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }

    def get(self) -> WorkersCumulativeStatisticsContext:
        """
        Constructs a WorkersCumulativeStatisticsContext

        """
        return WorkersCumulativeStatisticsContext(
            self._version, workspace_sid=self._solution["workspace_sid"]
        )

    def __call__(self) -> WorkersCumulativeStatisticsContext:
        """
        Constructs a WorkersCumulativeStatisticsContext

        """
        return WorkersCumulativeStatisticsContext(
            self._version, workspace_sid=self._solution["workspace_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkersCumulativeStatisticsList>"
