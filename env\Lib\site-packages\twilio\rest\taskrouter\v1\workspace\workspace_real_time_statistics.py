r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class WorkspaceRealTimeStatisticsInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Workspace resource.
    :ivar activity_statistics: The number of current Workers by Activity.
    :ivar longest_task_waiting_age: The age of the longest waiting Task.
    :ivar longest_task_waiting_sid: The SID of the longest waiting Task.
    :ivar tasks_by_priority: The number of Tasks by priority. For example: `{\"0\": \"10\", \"99\": \"5\"}` shows 10 Tasks at priority 0 and 5 at priority 99.
    :ivar tasks_by_status: The number of Tasks by their current status. For example: `{\"pending\": \"1\", \"reserved\": \"3\", \"assigned\": \"2\", \"completed\": \"5\"}`.
    :ivar total_tasks: The total number of Tasks.
    :ivar total_workers: The total number of Workers in the Workspace.
    :ivar workspace_sid: The SID of the Workspace.
    :ivar url: The absolute URL of the Workspace statistics resource.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], workspace_sid: str):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.activity_statistics: Optional[List[Dict[str, object]]] = payload.get(
            "activity_statistics"
        )
        self.longest_task_waiting_age: Optional[int] = deserialize.integer(
            payload.get("longest_task_waiting_age")
        )
        self.longest_task_waiting_sid: Optional[str] = payload.get(
            "longest_task_waiting_sid"
        )
        self.tasks_by_priority: Optional[Dict[str, object]] = payload.get(
            "tasks_by_priority"
        )
        self.tasks_by_status: Optional[Dict[str, object]] = payload.get(
            "tasks_by_status"
        )
        self.total_tasks: Optional[int] = deserialize.integer(
            payload.get("total_tasks")
        )
        self.total_workers: Optional[int] = deserialize.integer(
            payload.get("total_workers")
        )
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._context: Optional[WorkspaceRealTimeStatisticsContext] = None

    @property
    def _proxy(self) -> "WorkspaceRealTimeStatisticsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: WorkspaceRealTimeStatisticsContext for this WorkspaceRealTimeStatisticsInstance
        """
        if self._context is None:
            self._context = WorkspaceRealTimeStatisticsContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
            )
        return self._context

    def fetch(
        self, task_channel: Union[str, object] = values.unset
    ) -> "WorkspaceRealTimeStatisticsInstance":
        """
        Fetch the WorkspaceRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkspaceRealTimeStatisticsInstance
        """
        return self._proxy.fetch(
            task_channel=task_channel,
        )

    async def fetch_async(
        self, task_channel: Union[str, object] = values.unset
    ) -> "WorkspaceRealTimeStatisticsInstance":
        """
        Asynchronous coroutine to fetch the WorkspaceRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkspaceRealTimeStatisticsInstance
        """
        return await self._proxy.fetch_async(
            task_channel=task_channel,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkspaceRealTimeStatisticsInstance {}>".format(
            context
        )


class WorkspaceRealTimeStatisticsContext(InstanceContext):

    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkspaceRealTimeStatisticsContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace to fetch.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/RealTimeStatistics".format(
            **self._solution
        )

    def fetch(
        self, task_channel: Union[str, object] = values.unset
    ) -> WorkspaceRealTimeStatisticsInstance:
        """
        Fetch the WorkspaceRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkspaceRealTimeStatisticsInstance
        """

        data = values.of(
            {
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return WorkspaceRealTimeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
        )

    async def fetch_async(
        self, task_channel: Union[str, object] = values.unset
    ) -> WorkspaceRealTimeStatisticsInstance:
        """
        Asynchronous coroutine to fetch the WorkspaceRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkspaceRealTimeStatisticsInstance
        """

        data = values.of(
            {
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return WorkspaceRealTimeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkspaceRealTimeStatisticsContext {}>".format(
            context
        )


class WorkspaceRealTimeStatisticsList(ListResource):

    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkspaceRealTimeStatisticsList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace to fetch.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }

    def get(self) -> WorkspaceRealTimeStatisticsContext:
        """
        Constructs a WorkspaceRealTimeStatisticsContext

        """
        return WorkspaceRealTimeStatisticsContext(
            self._version, workspace_sid=self._solution["workspace_sid"]
        )

    def __call__(self) -> WorkspaceRealTimeStatisticsContext:
        """
        Constructs a WorkspaceRealTimeStatisticsContext

        """
        return WorkspaceRealTimeStatisticsContext(
            self._version, workspace_sid=self._solution["workspace_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkspaceRealTimeStatisticsList>"
