r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Serverless
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class VariableInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Variable resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Variable resource.
    :ivar service_sid: The SID of the Service that the Variable resource is associated with.
    :ivar environment_sid: The SID of the Environment in which the Variable exists.
    :ivar key: A string by which the Variable resource can be referenced.
    :ivar value: A string that contains the actual value of the Variable.
    :ivar date_created: The date and time in GMT when the Variable resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the Variable resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Variable resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        service_sid: str,
        environment_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.service_sid: Optional[str] = payload.get("service_sid")
        self.environment_sid: Optional[str] = payload.get("environment_sid")
        self.key: Optional[str] = payload.get("key")
        self.value: Optional[str] = payload.get("value")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "service_sid": service_sid,
            "environment_sid": environment_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[VariableContext] = None

    @property
    def _proxy(self) -> "VariableContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: VariableContext for this VariableInstance
        """
        if self._context is None:
            self._context = VariableContext(
                self._version,
                service_sid=self._solution["service_sid"],
                environment_sid=self._solution["environment_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the VariableInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the VariableInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "VariableInstance":
        """
        Fetch the VariableInstance


        :returns: The fetched VariableInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "VariableInstance":
        """
        Asynchronous coroutine to fetch the VariableInstance


        :returns: The fetched VariableInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        key: Union[str, object] = values.unset,
        value: Union[str, object] = values.unset,
    ) -> "VariableInstance":
        """
        Update the VariableInstance

        :param key: A string by which the Variable resource can be referenced. It can be a maximum of 128 characters.
        :param value: A string that contains the actual value of the Variable. It can be a maximum of 450 bytes in size.

        :returns: The updated VariableInstance
        """
        return self._proxy.update(
            key=key,
            value=value,
        )

    async def update_async(
        self,
        key: Union[str, object] = values.unset,
        value: Union[str, object] = values.unset,
    ) -> "VariableInstance":
        """
        Asynchronous coroutine to update the VariableInstance

        :param key: A string by which the Variable resource can be referenced. It can be a maximum of 128 characters.
        :param value: A string that contains the actual value of the Variable. It can be a maximum of 450 bytes in size.

        :returns: The updated VariableInstance
        """
        return await self._proxy.update_async(
            key=key,
            value=value,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Serverless.V1.VariableInstance {}>".format(context)


class VariableContext(InstanceContext):

    def __init__(
        self, version: Version, service_sid: str, environment_sid: str, sid: str
    ):
        """
        Initialize the VariableContext

        :param version: Version that contains the resource
        :param service_sid: The SID of the Service to update the Variable resource under.
        :param environment_sid: The SID of the Environment with the Variable resource to update.
        :param sid: The SID of the Variable resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "environment_sid": environment_sid,
            "sid": sid,
        }
        self._uri = "/Services/{service_sid}/Environments/{environment_sid}/Variables/{sid}".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the VariableInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the VariableInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> VariableInstance:
        """
        Fetch the VariableInstance


        :returns: The fetched VariableInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return VariableInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> VariableInstance:
        """
        Asynchronous coroutine to fetch the VariableInstance


        :returns: The fetched VariableInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return VariableInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        key: Union[str, object] = values.unset,
        value: Union[str, object] = values.unset,
    ) -> VariableInstance:
        """
        Update the VariableInstance

        :param key: A string by which the Variable resource can be referenced. It can be a maximum of 128 characters.
        :param value: A string that contains the actual value of the Variable. It can be a maximum of 450 bytes in size.

        :returns: The updated VariableInstance
        """

        data = values.of(
            {
                "Key": key,
                "Value": value,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return VariableInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        key: Union[str, object] = values.unset,
        value: Union[str, object] = values.unset,
    ) -> VariableInstance:
        """
        Asynchronous coroutine to update the VariableInstance

        :param key: A string by which the Variable resource can be referenced. It can be a maximum of 128 characters.
        :param value: A string that contains the actual value of the Variable. It can be a maximum of 450 bytes in size.

        :returns: The updated VariableInstance
        """

        data = values.of(
            {
                "Key": key,
                "Value": value,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return VariableInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Serverless.V1.VariableContext {}>".format(context)


class VariablePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> VariableInstance:
        """
        Build an instance of VariableInstance

        :param payload: Payload response from the API
        """
        return VariableInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Serverless.V1.VariablePage>"


class VariableList(ListResource):

    def __init__(self, version: Version, service_sid: str, environment_sid: str):
        """
        Initialize the VariableList

        :param version: Version that contains the resource
        :param service_sid: The SID of the Service to read the Variable resources from.
        :param environment_sid: The SID of the Environment with the Variable resources to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "service_sid": service_sid,
            "environment_sid": environment_sid,
        }
        self._uri = (
            "/Services/{service_sid}/Environments/{environment_sid}/Variables".format(
                **self._solution
            )
        )

    def create(self, key: str, value: str) -> VariableInstance:
        """
        Create the VariableInstance

        :param key: A string by which the Variable resource can be referenced. It can be a maximum of 128 characters.
        :param value: A string that contains the actual value of the Variable. It can be a maximum of 450 bytes in size.

        :returns: The created VariableInstance
        """

        data = values.of(
            {
                "Key": key,
                "Value": value,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return VariableInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
        )

    async def create_async(self, key: str, value: str) -> VariableInstance:
        """
        Asynchronously create the VariableInstance

        :param key: A string by which the Variable resource can be referenced. It can be a maximum of 128 characters.
        :param value: A string that contains the actual value of the Variable. It can be a maximum of 450 bytes in size.

        :returns: The created VariableInstance
        """

        data = values.of(
            {
                "Key": key,
                "Value": value,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return VariableInstance(
            self._version,
            payload,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[VariableInstance]:
        """
        Streams VariableInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[VariableInstance]:
        """
        Asynchronously streams VariableInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[VariableInstance]:
        """
        Lists VariableInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[VariableInstance]:
        """
        Asynchronously lists VariableInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> VariablePage:
        """
        Retrieve a single page of VariableInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of VariableInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return VariablePage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> VariablePage:
        """
        Asynchronously retrieve a single page of VariableInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of VariableInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return VariablePage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> VariablePage:
        """
        Retrieve a specific page of VariableInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of VariableInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return VariablePage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> VariablePage:
        """
        Asynchronously retrieve a specific page of VariableInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of VariableInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return VariablePage(self._version, response, self._solution)

    def get(self, sid: str) -> VariableContext:
        """
        Constructs a VariableContext

        :param sid: The SID of the Variable resource to update.
        """
        return VariableContext(
            self._version,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> VariableContext:
        """
        Constructs a VariableContext

        :param sid: The SID of the Variable resource to update.
        """
        return VariableContext(
            self._version,
            service_sid=self._solution["service_sid"],
            environment_sid=self._solution["environment_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Serverless.V1.VariableList>"
