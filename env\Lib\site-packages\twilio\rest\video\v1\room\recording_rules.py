r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Video
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from twilio.base import deserialize, serialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class RecordingRulesInstance(InstanceResource):
    """
    :ivar room_sid: The SID of the Room resource for the Recording Rules
    :ivar rules: A collection of Recording Rules that describe how to include or exclude matching tracks for recording
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    """

    def __init__(self, version: Version, payload: Dict[str, Any], room_sid: str):
        super().__init__(version)

        self.room_sid: Optional[str] = payload.get("room_sid")
        self.rules: Optional[List[str]] = payload.get("rules")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )

        self._solution = {
            "room_sid": room_sid,
        }

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Video.V1.RecordingRulesInstance {}>".format(context)


class RecordingRulesList(ListResource):

    def __init__(self, version: Version, room_sid: str):
        """
        Initialize the RecordingRulesList

        :param version: Version that contains the resource
        :param room_sid: The SID of the Room resource where the recording rules to update apply.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "room_sid": room_sid,
        }
        self._uri = "/Rooms/{room_sid}/RecordingRules".format(**self._solution)

    def fetch(self) -> RecordingRulesInstance:
        """
        Asynchronously fetch the RecordingRulesInstance


        :returns: The fetched RecordingRulesInstance
        """
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return RecordingRulesInstance(
            self._version, payload, room_sid=self._solution["room_sid"]
        )

    async def fetch_async(self) -> RecordingRulesInstance:
        """
        Asynchronously fetch the RecordingRulesInstance


        :returns: The fetched RecordingRulesInstance
        """
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return RecordingRulesInstance(
            self._version, payload, room_sid=self._solution["room_sid"]
        )

    def update(
        self, rules: Union[object, object] = values.unset
    ) -> RecordingRulesInstance:
        """
        Update the RecordingRulesInstance

        :param rules: A JSON-encoded array of recording rules.

        :returns: The created RecordingRulesInstance
        """

        data = values.of(
            {
                "Rules": serialize.object(rules),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return RecordingRulesInstance(
            self._version, payload, room_sid=self._solution["room_sid"]
        )

    async def update_async(
        self, rules: Union[object, object] = values.unset
    ) -> RecordingRulesInstance:
        """
        Asynchronously update the RecordingRulesInstance

        :param rules: A JSON-encoded array of recording rules.

        :returns: The created RecordingRulesInstance
        """

        data = values.of(
            {
                "Rules": serialize.object(rules),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return RecordingRulesInstance(
            self._version, payload, room_sid=self._solution["room_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Video.V1.RecordingRulesList>"
