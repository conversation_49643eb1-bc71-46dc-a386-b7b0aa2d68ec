r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Trusthub
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class TrustProductsEntityAssignmentsInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Item Assignment resource.
    :ivar trust_product_sid: The unique string that we created to identify the TrustProduct resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Item Assignment resource.
    :ivar object_sid: The SID of an object bag that holds information of the different items.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Identity resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        trust_product_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.trust_product_sid: Optional[str] = payload.get("trust_product_sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.object_sid: Optional[str] = payload.get("object_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "trust_product_sid": trust_product_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[TrustProductsEntityAssignmentsContext] = None

    @property
    def _proxy(self) -> "TrustProductsEntityAssignmentsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: TrustProductsEntityAssignmentsContext for this TrustProductsEntityAssignmentsInstance
        """
        if self._context is None:
            self._context = TrustProductsEntityAssignmentsContext(
                self._version,
                trust_product_sid=self._solution["trust_product_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the TrustProductsEntityAssignmentsInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the TrustProductsEntityAssignmentsInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "TrustProductsEntityAssignmentsInstance":
        """
        Fetch the TrustProductsEntityAssignmentsInstance


        :returns: The fetched TrustProductsEntityAssignmentsInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "TrustProductsEntityAssignmentsInstance":
        """
        Asynchronous coroutine to fetch the TrustProductsEntityAssignmentsInstance


        :returns: The fetched TrustProductsEntityAssignmentsInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Trusthub.V1.TrustProductsEntityAssignmentsInstance {}>".format(
            context
        )


class TrustProductsEntityAssignmentsContext(InstanceContext):

    def __init__(self, version: Version, trust_product_sid: str, sid: str):
        """
        Initialize the TrustProductsEntityAssignmentsContext

        :param version: Version that contains the resource
        :param trust_product_sid: The unique string that we created to identify the TrustProduct resource.
        :param sid: The unique string that we created to identify the Identity resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "trust_product_sid": trust_product_sid,
            "sid": sid,
        }
        self._uri = "/TrustProducts/{trust_product_sid}/EntityAssignments/{sid}".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the TrustProductsEntityAssignmentsInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the TrustProductsEntityAssignmentsInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> TrustProductsEntityAssignmentsInstance:
        """
        Fetch the TrustProductsEntityAssignmentsInstance


        :returns: The fetched TrustProductsEntityAssignmentsInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return TrustProductsEntityAssignmentsInstance(
            self._version,
            payload,
            trust_product_sid=self._solution["trust_product_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> TrustProductsEntityAssignmentsInstance:
        """
        Asynchronous coroutine to fetch the TrustProductsEntityAssignmentsInstance


        :returns: The fetched TrustProductsEntityAssignmentsInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return TrustProductsEntityAssignmentsInstance(
            self._version,
            payload,
            trust_product_sid=self._solution["trust_product_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Trusthub.V1.TrustProductsEntityAssignmentsContext {}>".format(
            context
        )


class TrustProductsEntityAssignmentsPage(Page):

    def get_instance(
        self, payload: Dict[str, Any]
    ) -> TrustProductsEntityAssignmentsInstance:
        """
        Build an instance of TrustProductsEntityAssignmentsInstance

        :param payload: Payload response from the API
        """
        return TrustProductsEntityAssignmentsInstance(
            self._version,
            payload,
            trust_product_sid=self._solution["trust_product_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Trusthub.V1.TrustProductsEntityAssignmentsPage>"


class TrustProductsEntityAssignmentsList(ListResource):

    def __init__(self, version: Version, trust_product_sid: str):
        """
        Initialize the TrustProductsEntityAssignmentsList

        :param version: Version that contains the resource
        :param trust_product_sid: The unique string that we created to identify the TrustProduct resource.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "trust_product_sid": trust_product_sid,
        }
        self._uri = "/TrustProducts/{trust_product_sid}/EntityAssignments".format(
            **self._solution
        )

    def create(self, object_sid: str) -> TrustProductsEntityAssignmentsInstance:
        """
        Create the TrustProductsEntityAssignmentsInstance

        :param object_sid: The SID of an object bag that holds information of the different items.

        :returns: The created TrustProductsEntityAssignmentsInstance
        """

        data = values.of(
            {
                "ObjectSid": object_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TrustProductsEntityAssignmentsInstance(
            self._version,
            payload,
            trust_product_sid=self._solution["trust_product_sid"],
        )

    async def create_async(
        self, object_sid: str
    ) -> TrustProductsEntityAssignmentsInstance:
        """
        Asynchronously create the TrustProductsEntityAssignmentsInstance

        :param object_sid: The SID of an object bag that holds information of the different items.

        :returns: The created TrustProductsEntityAssignmentsInstance
        """

        data = values.of(
            {
                "ObjectSid": object_sid,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TrustProductsEntityAssignmentsInstance(
            self._version,
            payload,
            trust_product_sid=self._solution["trust_product_sid"],
        )

    def stream(
        self,
        object_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[TrustProductsEntityAssignmentsInstance]:
        """
        Streams TrustProductsEntityAssignmentsInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str object_type: A string to filter the results by (EndUserType or SupportingDocumentType) machine-name. This is useful when you want to retrieve the entity-assignment of a specific end-user or supporting document.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(object_type=object_type, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        object_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[TrustProductsEntityAssignmentsInstance]:
        """
        Asynchronously streams TrustProductsEntityAssignmentsInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str object_type: A string to filter the results by (EndUserType or SupportingDocumentType) machine-name. This is useful when you want to retrieve the entity-assignment of a specific end-user or supporting document.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            object_type=object_type, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        object_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TrustProductsEntityAssignmentsInstance]:
        """
        Lists TrustProductsEntityAssignmentsInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str object_type: A string to filter the results by (EndUserType or SupportingDocumentType) machine-name. This is useful when you want to retrieve the entity-assignment of a specific end-user or supporting document.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                object_type=object_type,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        object_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TrustProductsEntityAssignmentsInstance]:
        """
        Asynchronously lists TrustProductsEntityAssignmentsInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str object_type: A string to filter the results by (EndUserType or SupportingDocumentType) machine-name. This is useful when you want to retrieve the entity-assignment of a specific end-user or supporting document.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                object_type=object_type,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        object_type: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TrustProductsEntityAssignmentsPage:
        """
        Retrieve a single page of TrustProductsEntityAssignmentsInstance records from the API.
        Request is executed immediately

        :param object_type: A string to filter the results by (EndUserType or SupportingDocumentType) machine-name. This is useful when you want to retrieve the entity-assignment of a specific end-user or supporting document.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TrustProductsEntityAssignmentsInstance
        """
        data = values.of(
            {
                "ObjectType": object_type,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return TrustProductsEntityAssignmentsPage(
            self._version, response, self._solution
        )

    async def page_async(
        self,
        object_type: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TrustProductsEntityAssignmentsPage:
        """
        Asynchronously retrieve a single page of TrustProductsEntityAssignmentsInstance records from the API.
        Request is executed immediately

        :param object_type: A string to filter the results by (EndUserType or SupportingDocumentType) machine-name. This is useful when you want to retrieve the entity-assignment of a specific end-user or supporting document.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TrustProductsEntityAssignmentsInstance
        """
        data = values.of(
            {
                "ObjectType": object_type,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return TrustProductsEntityAssignmentsPage(
            self._version, response, self._solution
        )

    def get_page(self, target_url: str) -> TrustProductsEntityAssignmentsPage:
        """
        Retrieve a specific page of TrustProductsEntityAssignmentsInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TrustProductsEntityAssignmentsInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return TrustProductsEntityAssignmentsPage(
            self._version, response, self._solution
        )

    async def get_page_async(
        self, target_url: str
    ) -> TrustProductsEntityAssignmentsPage:
        """
        Asynchronously retrieve a specific page of TrustProductsEntityAssignmentsInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TrustProductsEntityAssignmentsInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return TrustProductsEntityAssignmentsPage(
            self._version, response, self._solution
        )

    def get(self, sid: str) -> TrustProductsEntityAssignmentsContext:
        """
        Constructs a TrustProductsEntityAssignmentsContext

        :param sid: The unique string that we created to identify the Identity resource.
        """
        return TrustProductsEntityAssignmentsContext(
            self._version,
            trust_product_sid=self._solution["trust_product_sid"],
            sid=sid,
        )

    def __call__(self, sid: str) -> TrustProductsEntityAssignmentsContext:
        """
        Constructs a TrustProductsEntityAssignmentsContext

        :param sid: The unique string that we created to identify the Identity resource.
        """
        return TrustProductsEntityAssignmentsContext(
            self._version,
            trust_product_sid=self._solution["trust_product_sid"],
            sid=sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Trusthub.V1.TrustProductsEntityAssignmentsList>"
