r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Flex
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ConfiguredPluginInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that the Flex Plugin resource is installed for.
    :ivar configuration_sid: The SID of the Flex Plugin Configuration that this Flex Plugin belongs to.
    :ivar plugin_sid: The SID of the Flex Plugin.
    :ivar plugin_version_sid: The SID of the Flex Plugin Version.
    :ivar phase: The phase this Flex Plugin would initialize at runtime.
    :ivar plugin_url: The URL of where the Flex Plugin Version JavaScript bundle is hosted on.
    :ivar unique_name: The name that uniquely identifies this Flex Plugin resource.
    :ivar friendly_name: The friendly name of this Flex Plugin resource.
    :ivar description: A descriptive string that you create to describe the plugin resource. It can be up to 500 characters long
    :ivar plugin_archived: Whether the Flex Plugin is archived. The default value is false.
    :ivar version: The latest version of this Flex Plugin Version.
    :ivar changelog: A changelog that describes the changes this Flex Plugin Version brings.
    :ivar plugin_version_archived: Whether the Flex Plugin Version is archived. The default value is false.
    :ivar private: Whether to validate the request is authorized to access the Flex Plugin Version.
    :ivar date_created: The date and time in GMT when the Flex Plugin was installed specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar url: The absolute URL of the Flex Plugin resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        configuration_sid: str,
        plugin_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.configuration_sid: Optional[str] = payload.get("configuration_sid")
        self.plugin_sid: Optional[str] = payload.get("plugin_sid")
        self.plugin_version_sid: Optional[str] = payload.get("plugin_version_sid")
        self.phase: Optional[int] = deserialize.integer(payload.get("phase"))
        self.plugin_url: Optional[str] = payload.get("plugin_url")
        self.unique_name: Optional[str] = payload.get("unique_name")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.description: Optional[str] = payload.get("description")
        self.plugin_archived: Optional[bool] = payload.get("plugin_archived")
        self.version: Optional[str] = payload.get("version")
        self.changelog: Optional[str] = payload.get("changelog")
        self.plugin_version_archived: Optional[bool] = payload.get(
            "plugin_version_archived"
        )
        self.private: Optional[bool] = payload.get("private")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "configuration_sid": configuration_sid,
            "plugin_sid": plugin_sid or self.plugin_sid,
        }
        self._context: Optional[ConfiguredPluginContext] = None

    @property
    def _proxy(self) -> "ConfiguredPluginContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ConfiguredPluginContext for this ConfiguredPluginInstance
        """
        if self._context is None:
            self._context = ConfiguredPluginContext(
                self._version,
                configuration_sid=self._solution["configuration_sid"],
                plugin_sid=self._solution["plugin_sid"],
            )
        return self._context

    def fetch(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "ConfiguredPluginInstance":
        """
        Fetch the ConfiguredPluginInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched ConfiguredPluginInstance
        """
        return self._proxy.fetch(
            flex_metadata=flex_metadata,
        )

    async def fetch_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> "ConfiguredPluginInstance":
        """
        Asynchronous coroutine to fetch the ConfiguredPluginInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched ConfiguredPluginInstance
        """
        return await self._proxy.fetch_async(
            flex_metadata=flex_metadata,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.ConfiguredPluginInstance {}>".format(context)


class ConfiguredPluginContext(InstanceContext):

    def __init__(self, version: Version, configuration_sid: str, plugin_sid: str):
        """
        Initialize the ConfiguredPluginContext

        :param version: Version that contains the resource
        :param configuration_sid: The SID of the Flex Plugin Configuration the resource to belongs to.
        :param plugin_sid: The unique string that we created to identify the Flex Plugin resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "configuration_sid": configuration_sid,
            "plugin_sid": plugin_sid,
        }
        self._uri = "/PluginService/Configurations/{configuration_sid}/Plugins/{plugin_sid}".format(
            **self._solution
        )

    def fetch(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> ConfiguredPluginInstance:
        """
        Fetch the ConfiguredPluginInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched ConfiguredPluginInstance
        """

        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return ConfiguredPluginInstance(
            self._version,
            payload,
            configuration_sid=self._solution["configuration_sid"],
            plugin_sid=self._solution["plugin_sid"],
        )

    async def fetch_async(
        self, flex_metadata: Union[str, object] = values.unset
    ) -> ConfiguredPluginInstance:
        """
        Asynchronous coroutine to fetch the ConfiguredPluginInstance

        :param flex_metadata: The Flex-Metadata HTTP request header

        :returns: The fetched ConfiguredPluginInstance
        """

        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return ConfiguredPluginInstance(
            self._version,
            payload,
            configuration_sid=self._solution["configuration_sid"],
            plugin_sid=self._solution["plugin_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.FlexApi.V1.ConfiguredPluginContext {}>".format(context)


class ConfiguredPluginPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ConfiguredPluginInstance:
        """
        Build an instance of ConfiguredPluginInstance

        :param payload: Payload response from the API
        """
        return ConfiguredPluginInstance(
            self._version,
            payload,
            configuration_sid=self._solution["configuration_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.ConfiguredPluginPage>"


class ConfiguredPluginList(ListResource):

    def __init__(self, version: Version, configuration_sid: str):
        """
        Initialize the ConfiguredPluginList

        :param version: Version that contains the resource
        :param configuration_sid: The SID of the Flex Plugin Configuration the resource to belongs to.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "configuration_sid": configuration_sid,
        }
        self._uri = "/PluginService/Configurations/{configuration_sid}/Plugins".format(
            **self._solution
        )

    def stream(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ConfiguredPluginInstance]:
        """
        Streams ConfiguredPluginInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(flex_metadata=flex_metadata, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ConfiguredPluginInstance]:
        """
        Asynchronously streams ConfiguredPluginInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            flex_metadata=flex_metadata, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConfiguredPluginInstance]:
        """
        Lists ConfiguredPluginInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                flex_metadata=flex_metadata,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConfiguredPluginInstance]:
        """
        Asynchronously lists ConfiguredPluginInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str flex_metadata: The Flex-Metadata HTTP request header
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                flex_metadata=flex_metadata,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        flex_metadata: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConfiguredPluginPage:
        """
        Retrieve a single page of ConfiguredPluginInstance records from the API.
        Request is executed immediately

        :param flex_metadata: The Flex-Metadata HTTP request header
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConfiguredPluginInstance
        """
        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConfiguredPluginPage(self._version, response, self._solution)

    async def page_async(
        self,
        flex_metadata: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConfiguredPluginPage:
        """
        Asynchronously retrieve a single page of ConfiguredPluginInstance records from the API.
        Request is executed immediately

        :param flex_metadata: The Flex-Metadata HTTP request header
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConfiguredPluginInstance
        """
        data = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of(
            {
                "Flex-Metadata": flex_metadata,
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConfiguredPluginPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ConfiguredPluginPage:
        """
        Retrieve a specific page of ConfiguredPluginInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConfiguredPluginInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ConfiguredPluginPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ConfiguredPluginPage:
        """
        Asynchronously retrieve a specific page of ConfiguredPluginInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConfiguredPluginInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ConfiguredPluginPage(self._version, response, self._solution)

    def get(self, plugin_sid: str) -> ConfiguredPluginContext:
        """
        Constructs a ConfiguredPluginContext

        :param plugin_sid: The unique string that we created to identify the Flex Plugin resource.
        """
        return ConfiguredPluginContext(
            self._version,
            configuration_sid=self._solution["configuration_sid"],
            plugin_sid=plugin_sid,
        )

    def __call__(self, plugin_sid: str) -> ConfiguredPluginContext:
        """
        Constructs a ConfiguredPluginContext

        :param plugin_sid: The unique string that we created to identify the Flex Plugin resource.
        """
        return ConfiguredPluginContext(
            self._version,
            configuration_sid=self._solution["configuration_sid"],
            plugin_sid=plugin_sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.FlexApi.V1.ConfiguredPluginList>"
