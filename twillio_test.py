from twilio.rest import Client
from dotenv import load_dotenv
import os

load_dotenv()  # Load environment variables from .env file

account_sid = os.getenv("twillio_SID")
auth_token = os.getenv("twillio_Token")
number = os.getenv('TWILIO_NUMBER')
# Initialize Twilio client
client = Client(account_sid, auth_token)

# # Send an SMS
# message = client.messages.create(
#     body="Test message from celestial",
#     from_=number,  # Your Twilio phone number
#     to='+************'   # Recipient's phone number
# )

# print(f"Message sent with SID: {message.sid}")



# Initialize Twilio client
client = Client(account_sid, auth_token)

# Make a call
call = client.calls.create(
    url='http://demo.twilio.com/docs/voice.xml',  # TwiML instructions for the call
    from_=number,  # Your Twilio phone number
    to='+***********'   # Recipient's phone number
)

print(f"Call initiated with SID: {call.sid}")