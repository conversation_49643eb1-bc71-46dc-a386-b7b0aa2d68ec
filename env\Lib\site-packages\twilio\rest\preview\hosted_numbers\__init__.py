r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Preview
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.preview.hosted_numbers.authorization_document import (
    AuthorizationDocumentList,
)
from twilio.rest.preview.hosted_numbers.hosted_number_order import HostedNumberOrderList


class HostedNumbers(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the HostedNumbers version of Preview

        :param domain: The Twilio.preview domain
        """
        super().__init__(domain, "HostedNumbers")
        self._authorization_documents: Optional[AuthorizationDocumentList] = None
        self._hosted_number_orders: Optional[HostedNumberOrderList] = None

    @property
    def authorization_documents(self) -> AuthorizationDocumentList:
        if self._authorization_documents is None:
            self._authorization_documents = AuthorizationDocumentList(self)
        return self._authorization_documents

    @property
    def hosted_number_orders(self) -> HostedNumberOrderList:
        if self._hosted_number_orders is None:
            self._hosted_number_orders = HostedNumberOrderList(self)
        return self._hosted_number_orders

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Preview.HostedNumbers>"
