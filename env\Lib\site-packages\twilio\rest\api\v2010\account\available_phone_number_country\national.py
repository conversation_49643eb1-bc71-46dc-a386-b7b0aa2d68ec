r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class NationalInstance(InstanceResource):
    """
    :ivar friendly_name: A formatted version of the phone number.
    :ivar phone_number: The phone number in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, which consists of a + followed by the country code and subscriber number.
    :ivar lata: The [LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area) of this phone number. Available for only phone numbers from the US and Canada.
    :ivar locality: The locality or city of this phone number's location.
    :ivar rate_center: The [rate center](https://en.wikipedia.org/wiki/Telephone_exchange) of this phone number. Available for only phone numbers from the US and Canada.
    :ivar latitude: The latitude of this phone number's location. Available for only phone numbers from the US and Canada.
    :ivar longitude: The longitude of this phone number's location. Available for only phone numbers from the US and Canada.
    :ivar region: The two-letter state or province abbreviation of this phone number's location. Available for only phone numbers from the US and Canada.
    :ivar postal_code: The postal or ZIP code of this phone number's location. Available for only phone numbers from the US and Canada.
    :ivar iso_country: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of this phone number.
    :ivar address_requirements: The type of [Address](https://www.twilio.com/docs/usage/api/address) resource the phone number requires. Can be: `none`, `any`, `local`, or `foreign`. `none` means no address is required. `any` means an address is required, but it can be anywhere in the world. `local` means an address in the phone number's country is required. `foreign` means an address outside of the phone number's country is required.
    :ivar beta: Whether the phone number is new to the Twilio platform. Can be: `true` or `false`.
    :ivar capabilities:
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        country_code: str,
    ):
        super().__init__(version)

        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.phone_number: Optional[str] = payload.get("phone_number")
        self.lata: Optional[str] = payload.get("lata")
        self.locality: Optional[str] = payload.get("locality")
        self.rate_center: Optional[str] = payload.get("rate_center")
        self.latitude: Optional[float] = deserialize.decimal(payload.get("latitude"))
        self.longitude: Optional[float] = deserialize.decimal(payload.get("longitude"))
        self.region: Optional[str] = payload.get("region")
        self.postal_code: Optional[str] = payload.get("postal_code")
        self.iso_country: Optional[str] = payload.get("iso_country")
        self.address_requirements: Optional[str] = payload.get("address_requirements")
        self.beta: Optional[bool] = payload.get("beta")
        self.capabilities: Optional[str] = payload.get("capabilities")

        self._solution = {
            "account_sid": account_sid,
            "country_code": country_code,
        }

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.NationalInstance {}>".format(context)


class NationalPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> NationalInstance:
        """
        Build an instance of NationalInstance

        :param payload: Payload response from the API
        """
        return NationalInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            country_code=self._solution["country_code"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.NationalPage>"


class NationalList(ListResource):

    def __init__(self, version: Version, account_sid: str, country_code: str):
        """
        Initialize the NationalList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) requesting the AvailablePhoneNumber resources.
        :param country_code: The [ISO-3166-1](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) country code of the country from which to read phone numbers.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "country_code": country_code,
        }
        self._uri = "/Accounts/{account_sid}/AvailablePhoneNumbers/{country_code}/National.json".format(
            **self._solution
        )

    def stream(
        self,
        area_code: Union[int, object] = values.unset,
        contains: Union[str, object] = values.unset,
        sms_enabled: Union[bool, object] = values.unset,
        mms_enabled: Union[bool, object] = values.unset,
        voice_enabled: Union[bool, object] = values.unset,
        exclude_all_address_required: Union[bool, object] = values.unset,
        exclude_local_address_required: Union[bool, object] = values.unset,
        exclude_foreign_address_required: Union[bool, object] = values.unset,
        beta: Union[bool, object] = values.unset,
        near_number: Union[str, object] = values.unset,
        near_lat_long: Union[str, object] = values.unset,
        distance: Union[int, object] = values.unset,
        in_postal_code: Union[str, object] = values.unset,
        in_region: Union[str, object] = values.unset,
        in_rate_center: Union[str, object] = values.unset,
        in_lata: Union[str, object] = values.unset,
        in_locality: Union[str, object] = values.unset,
        fax_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[NationalInstance]:
        """
        Streams NationalInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param int area_code: The area code of the phone numbers to read. Applies to only phone numbers in the US and Canada.
        :param str contains: The pattern on which to match phone numbers. Valid characters are `*`, `0-9`, `a-z`, and `A-Z`. The `*` character matches any single digit. For examples, see [Example 2](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-2) and [Example 3](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-3). If specified, this value must have at least two characters.
        :param bool sms_enabled: Whether the phone numbers can receive text messages. Can be: `true` or `false`.
        :param bool mms_enabled: Whether the phone numbers can receive MMS messages. Can be: `true` or `false`.
        :param bool voice_enabled: Whether the phone numbers can receive calls. Can be: `true` or `false`.
        :param bool exclude_all_address_required: Whether to exclude phone numbers that require an [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_local_address_required: Whether to exclude phone numbers that require a local [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_foreign_address_required: Whether to exclude phone numbers that require a foreign [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool beta: Whether to read phone numbers that are new to the Twilio platform. Can be: `true` or `false` and the default is `true`.
        :param str near_number: Given a phone number, find a geographically close number within `distance` miles. Distance defaults to 25 miles. Applies to only phone numbers in the US and Canada.
        :param str near_lat_long: Given a latitude/longitude pair `lat,long` find geographically close numbers within `distance` miles. Applies to only phone numbers in the US and Canada.
        :param int distance: The search radius, in miles, for a `near_` query.  Can be up to `500` and the default is `25`. Applies to only phone numbers in the US and Canada.
        :param str in_postal_code: Limit results to a particular postal code. Given a phone number, search within the same postal code as that number. Applies to only phone numbers in the US and Canada.
        :param str in_region: Limit results to a particular region, state, or province. Given a phone number, search within the same region as that number. Applies to only phone numbers in the US and Canada.
        :param str in_rate_center: Limit results to a specific rate center, or given a phone number search within the same rate center as that number. Requires `in_lata` to be set as well. Applies to only phone numbers in the US and Canada.
        :param str in_lata: Limit results to a specific local access and transport area ([LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area)). Given a phone number, search within the same [LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area) as that number. Applies to only phone numbers in the US and Canada.
        :param str in_locality: Limit results to a particular locality or city. Given a phone number, search within the same Locality as that number.
        :param bool fax_enabled: Whether the phone numbers can receive faxes. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            area_code=area_code,
            contains=contains,
            sms_enabled=sms_enabled,
            mms_enabled=mms_enabled,
            voice_enabled=voice_enabled,
            exclude_all_address_required=exclude_all_address_required,
            exclude_local_address_required=exclude_local_address_required,
            exclude_foreign_address_required=exclude_foreign_address_required,
            beta=beta,
            near_number=near_number,
            near_lat_long=near_lat_long,
            distance=distance,
            in_postal_code=in_postal_code,
            in_region=in_region,
            in_rate_center=in_rate_center,
            in_lata=in_lata,
            in_locality=in_locality,
            fax_enabled=fax_enabled,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        area_code: Union[int, object] = values.unset,
        contains: Union[str, object] = values.unset,
        sms_enabled: Union[bool, object] = values.unset,
        mms_enabled: Union[bool, object] = values.unset,
        voice_enabled: Union[bool, object] = values.unset,
        exclude_all_address_required: Union[bool, object] = values.unset,
        exclude_local_address_required: Union[bool, object] = values.unset,
        exclude_foreign_address_required: Union[bool, object] = values.unset,
        beta: Union[bool, object] = values.unset,
        near_number: Union[str, object] = values.unset,
        near_lat_long: Union[str, object] = values.unset,
        distance: Union[int, object] = values.unset,
        in_postal_code: Union[str, object] = values.unset,
        in_region: Union[str, object] = values.unset,
        in_rate_center: Union[str, object] = values.unset,
        in_lata: Union[str, object] = values.unset,
        in_locality: Union[str, object] = values.unset,
        fax_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[NationalInstance]:
        """
        Asynchronously streams NationalInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param int area_code: The area code of the phone numbers to read. Applies to only phone numbers in the US and Canada.
        :param str contains: The pattern on which to match phone numbers. Valid characters are `*`, `0-9`, `a-z`, and `A-Z`. The `*` character matches any single digit. For examples, see [Example 2](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-2) and [Example 3](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-3). If specified, this value must have at least two characters.
        :param bool sms_enabled: Whether the phone numbers can receive text messages. Can be: `true` or `false`.
        :param bool mms_enabled: Whether the phone numbers can receive MMS messages. Can be: `true` or `false`.
        :param bool voice_enabled: Whether the phone numbers can receive calls. Can be: `true` or `false`.
        :param bool exclude_all_address_required: Whether to exclude phone numbers that require an [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_local_address_required: Whether to exclude phone numbers that require a local [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_foreign_address_required: Whether to exclude phone numbers that require a foreign [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool beta: Whether to read phone numbers that are new to the Twilio platform. Can be: `true` or `false` and the default is `true`.
        :param str near_number: Given a phone number, find a geographically close number within `distance` miles. Distance defaults to 25 miles. Applies to only phone numbers in the US and Canada.
        :param str near_lat_long: Given a latitude/longitude pair `lat,long` find geographically close numbers within `distance` miles. Applies to only phone numbers in the US and Canada.
        :param int distance: The search radius, in miles, for a `near_` query.  Can be up to `500` and the default is `25`. Applies to only phone numbers in the US and Canada.
        :param str in_postal_code: Limit results to a particular postal code. Given a phone number, search within the same postal code as that number. Applies to only phone numbers in the US and Canada.
        :param str in_region: Limit results to a particular region, state, or province. Given a phone number, search within the same region as that number. Applies to only phone numbers in the US and Canada.
        :param str in_rate_center: Limit results to a specific rate center, or given a phone number search within the same rate center as that number. Requires `in_lata` to be set as well. Applies to only phone numbers in the US and Canada.
        :param str in_lata: Limit results to a specific local access and transport area ([LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area)). Given a phone number, search within the same [LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area) as that number. Applies to only phone numbers in the US and Canada.
        :param str in_locality: Limit results to a particular locality or city. Given a phone number, search within the same Locality as that number.
        :param bool fax_enabled: Whether the phone numbers can receive faxes. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            area_code=area_code,
            contains=contains,
            sms_enabled=sms_enabled,
            mms_enabled=mms_enabled,
            voice_enabled=voice_enabled,
            exclude_all_address_required=exclude_all_address_required,
            exclude_local_address_required=exclude_local_address_required,
            exclude_foreign_address_required=exclude_foreign_address_required,
            beta=beta,
            near_number=near_number,
            near_lat_long=near_lat_long,
            distance=distance,
            in_postal_code=in_postal_code,
            in_region=in_region,
            in_rate_center=in_rate_center,
            in_lata=in_lata,
            in_locality=in_locality,
            fax_enabled=fax_enabled,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        area_code: Union[int, object] = values.unset,
        contains: Union[str, object] = values.unset,
        sms_enabled: Union[bool, object] = values.unset,
        mms_enabled: Union[bool, object] = values.unset,
        voice_enabled: Union[bool, object] = values.unset,
        exclude_all_address_required: Union[bool, object] = values.unset,
        exclude_local_address_required: Union[bool, object] = values.unset,
        exclude_foreign_address_required: Union[bool, object] = values.unset,
        beta: Union[bool, object] = values.unset,
        near_number: Union[str, object] = values.unset,
        near_lat_long: Union[str, object] = values.unset,
        distance: Union[int, object] = values.unset,
        in_postal_code: Union[str, object] = values.unset,
        in_region: Union[str, object] = values.unset,
        in_rate_center: Union[str, object] = values.unset,
        in_lata: Union[str, object] = values.unset,
        in_locality: Union[str, object] = values.unset,
        fax_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[NationalInstance]:
        """
        Lists NationalInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param int area_code: The area code of the phone numbers to read. Applies to only phone numbers in the US and Canada.
        :param str contains: The pattern on which to match phone numbers. Valid characters are `*`, `0-9`, `a-z`, and `A-Z`. The `*` character matches any single digit. For examples, see [Example 2](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-2) and [Example 3](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-3). If specified, this value must have at least two characters.
        :param bool sms_enabled: Whether the phone numbers can receive text messages. Can be: `true` or `false`.
        :param bool mms_enabled: Whether the phone numbers can receive MMS messages. Can be: `true` or `false`.
        :param bool voice_enabled: Whether the phone numbers can receive calls. Can be: `true` or `false`.
        :param bool exclude_all_address_required: Whether to exclude phone numbers that require an [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_local_address_required: Whether to exclude phone numbers that require a local [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_foreign_address_required: Whether to exclude phone numbers that require a foreign [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool beta: Whether to read phone numbers that are new to the Twilio platform. Can be: `true` or `false` and the default is `true`.
        :param str near_number: Given a phone number, find a geographically close number within `distance` miles. Distance defaults to 25 miles. Applies to only phone numbers in the US and Canada.
        :param str near_lat_long: Given a latitude/longitude pair `lat,long` find geographically close numbers within `distance` miles. Applies to only phone numbers in the US and Canada.
        :param int distance: The search radius, in miles, for a `near_` query.  Can be up to `500` and the default is `25`. Applies to only phone numbers in the US and Canada.
        :param str in_postal_code: Limit results to a particular postal code. Given a phone number, search within the same postal code as that number. Applies to only phone numbers in the US and Canada.
        :param str in_region: Limit results to a particular region, state, or province. Given a phone number, search within the same region as that number. Applies to only phone numbers in the US and Canada.
        :param str in_rate_center: Limit results to a specific rate center, or given a phone number search within the same rate center as that number. Requires `in_lata` to be set as well. Applies to only phone numbers in the US and Canada.
        :param str in_lata: Limit results to a specific local access and transport area ([LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area)). Given a phone number, search within the same [LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area) as that number. Applies to only phone numbers in the US and Canada.
        :param str in_locality: Limit results to a particular locality or city. Given a phone number, search within the same Locality as that number.
        :param bool fax_enabled: Whether the phone numbers can receive faxes. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                area_code=area_code,
                contains=contains,
                sms_enabled=sms_enabled,
                mms_enabled=mms_enabled,
                voice_enabled=voice_enabled,
                exclude_all_address_required=exclude_all_address_required,
                exclude_local_address_required=exclude_local_address_required,
                exclude_foreign_address_required=exclude_foreign_address_required,
                beta=beta,
                near_number=near_number,
                near_lat_long=near_lat_long,
                distance=distance,
                in_postal_code=in_postal_code,
                in_region=in_region,
                in_rate_center=in_rate_center,
                in_lata=in_lata,
                in_locality=in_locality,
                fax_enabled=fax_enabled,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        area_code: Union[int, object] = values.unset,
        contains: Union[str, object] = values.unset,
        sms_enabled: Union[bool, object] = values.unset,
        mms_enabled: Union[bool, object] = values.unset,
        voice_enabled: Union[bool, object] = values.unset,
        exclude_all_address_required: Union[bool, object] = values.unset,
        exclude_local_address_required: Union[bool, object] = values.unset,
        exclude_foreign_address_required: Union[bool, object] = values.unset,
        beta: Union[bool, object] = values.unset,
        near_number: Union[str, object] = values.unset,
        near_lat_long: Union[str, object] = values.unset,
        distance: Union[int, object] = values.unset,
        in_postal_code: Union[str, object] = values.unset,
        in_region: Union[str, object] = values.unset,
        in_rate_center: Union[str, object] = values.unset,
        in_lata: Union[str, object] = values.unset,
        in_locality: Union[str, object] = values.unset,
        fax_enabled: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[NationalInstance]:
        """
        Asynchronously lists NationalInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param int area_code: The area code of the phone numbers to read. Applies to only phone numbers in the US and Canada.
        :param str contains: The pattern on which to match phone numbers. Valid characters are `*`, `0-9`, `a-z`, and `A-Z`. The `*` character matches any single digit. For examples, see [Example 2](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-2) and [Example 3](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-3). If specified, this value must have at least two characters.
        :param bool sms_enabled: Whether the phone numbers can receive text messages. Can be: `true` or `false`.
        :param bool mms_enabled: Whether the phone numbers can receive MMS messages. Can be: `true` or `false`.
        :param bool voice_enabled: Whether the phone numbers can receive calls. Can be: `true` or `false`.
        :param bool exclude_all_address_required: Whether to exclude phone numbers that require an [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_local_address_required: Whether to exclude phone numbers that require a local [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool exclude_foreign_address_required: Whether to exclude phone numbers that require a foreign [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param bool beta: Whether to read phone numbers that are new to the Twilio platform. Can be: `true` or `false` and the default is `true`.
        :param str near_number: Given a phone number, find a geographically close number within `distance` miles. Distance defaults to 25 miles. Applies to only phone numbers in the US and Canada.
        :param str near_lat_long: Given a latitude/longitude pair `lat,long` find geographically close numbers within `distance` miles. Applies to only phone numbers in the US and Canada.
        :param int distance: The search radius, in miles, for a `near_` query.  Can be up to `500` and the default is `25`. Applies to only phone numbers in the US and Canada.
        :param str in_postal_code: Limit results to a particular postal code. Given a phone number, search within the same postal code as that number. Applies to only phone numbers in the US and Canada.
        :param str in_region: Limit results to a particular region, state, or province. Given a phone number, search within the same region as that number. Applies to only phone numbers in the US and Canada.
        :param str in_rate_center: Limit results to a specific rate center, or given a phone number search within the same rate center as that number. Requires `in_lata` to be set as well. Applies to only phone numbers in the US and Canada.
        :param str in_lata: Limit results to a specific local access and transport area ([LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area)). Given a phone number, search within the same [LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area) as that number. Applies to only phone numbers in the US and Canada.
        :param str in_locality: Limit results to a particular locality or city. Given a phone number, search within the same Locality as that number.
        :param bool fax_enabled: Whether the phone numbers can receive faxes. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                area_code=area_code,
                contains=contains,
                sms_enabled=sms_enabled,
                mms_enabled=mms_enabled,
                voice_enabled=voice_enabled,
                exclude_all_address_required=exclude_all_address_required,
                exclude_local_address_required=exclude_local_address_required,
                exclude_foreign_address_required=exclude_foreign_address_required,
                beta=beta,
                near_number=near_number,
                near_lat_long=near_lat_long,
                distance=distance,
                in_postal_code=in_postal_code,
                in_region=in_region,
                in_rate_center=in_rate_center,
                in_lata=in_lata,
                in_locality=in_locality,
                fax_enabled=fax_enabled,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        area_code: Union[int, object] = values.unset,
        contains: Union[str, object] = values.unset,
        sms_enabled: Union[bool, object] = values.unset,
        mms_enabled: Union[bool, object] = values.unset,
        voice_enabled: Union[bool, object] = values.unset,
        exclude_all_address_required: Union[bool, object] = values.unset,
        exclude_local_address_required: Union[bool, object] = values.unset,
        exclude_foreign_address_required: Union[bool, object] = values.unset,
        beta: Union[bool, object] = values.unset,
        near_number: Union[str, object] = values.unset,
        near_lat_long: Union[str, object] = values.unset,
        distance: Union[int, object] = values.unset,
        in_postal_code: Union[str, object] = values.unset,
        in_region: Union[str, object] = values.unset,
        in_rate_center: Union[str, object] = values.unset,
        in_lata: Union[str, object] = values.unset,
        in_locality: Union[str, object] = values.unset,
        fax_enabled: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> NationalPage:
        """
        Retrieve a single page of NationalInstance records from the API.
        Request is executed immediately

        :param area_code: The area code of the phone numbers to read. Applies to only phone numbers in the US and Canada.
        :param contains: The pattern on which to match phone numbers. Valid characters are `*`, `0-9`, `a-z`, and `A-Z`. The `*` character matches any single digit. For examples, see [Example 2](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-2) and [Example 3](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-3). If specified, this value must have at least two characters.
        :param sms_enabled: Whether the phone numbers can receive text messages. Can be: `true` or `false`.
        :param mms_enabled: Whether the phone numbers can receive MMS messages. Can be: `true` or `false`.
        :param voice_enabled: Whether the phone numbers can receive calls. Can be: `true` or `false`.
        :param exclude_all_address_required: Whether to exclude phone numbers that require an [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param exclude_local_address_required: Whether to exclude phone numbers that require a local [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param exclude_foreign_address_required: Whether to exclude phone numbers that require a foreign [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param beta: Whether to read phone numbers that are new to the Twilio platform. Can be: `true` or `false` and the default is `true`.
        :param near_number: Given a phone number, find a geographically close number within `distance` miles. Distance defaults to 25 miles. Applies to only phone numbers in the US and Canada.
        :param near_lat_long: Given a latitude/longitude pair `lat,long` find geographically close numbers within `distance` miles. Applies to only phone numbers in the US and Canada.
        :param distance: The search radius, in miles, for a `near_` query.  Can be up to `500` and the default is `25`. Applies to only phone numbers in the US and Canada.
        :param in_postal_code: Limit results to a particular postal code. Given a phone number, search within the same postal code as that number. Applies to only phone numbers in the US and Canada.
        :param in_region: Limit results to a particular region, state, or province. Given a phone number, search within the same region as that number. Applies to only phone numbers in the US and Canada.
        :param in_rate_center: Limit results to a specific rate center, or given a phone number search within the same rate center as that number. Requires `in_lata` to be set as well. Applies to only phone numbers in the US and Canada.
        :param in_lata: Limit results to a specific local access and transport area ([LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area)). Given a phone number, search within the same [LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area) as that number. Applies to only phone numbers in the US and Canada.
        :param in_locality: Limit results to a particular locality or city. Given a phone number, search within the same Locality as that number.
        :param fax_enabled: Whether the phone numbers can receive faxes. Can be: `true` or `false`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of NationalInstance
        """
        data = values.of(
            {
                "AreaCode": area_code,
                "Contains": contains,
                "SmsEnabled": serialize.boolean_to_string(sms_enabled),
                "MmsEnabled": serialize.boolean_to_string(mms_enabled),
                "VoiceEnabled": serialize.boolean_to_string(voice_enabled),
                "ExcludeAllAddressRequired": serialize.boolean_to_string(
                    exclude_all_address_required
                ),
                "ExcludeLocalAddressRequired": serialize.boolean_to_string(
                    exclude_local_address_required
                ),
                "ExcludeForeignAddressRequired": serialize.boolean_to_string(
                    exclude_foreign_address_required
                ),
                "Beta": serialize.boolean_to_string(beta),
                "NearNumber": near_number,
                "NearLatLong": near_lat_long,
                "Distance": distance,
                "InPostalCode": in_postal_code,
                "InRegion": in_region,
                "InRateCenter": in_rate_center,
                "InLata": in_lata,
                "InLocality": in_locality,
                "FaxEnabled": serialize.boolean_to_string(fax_enabled),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return NationalPage(self._version, response, self._solution)

    async def page_async(
        self,
        area_code: Union[int, object] = values.unset,
        contains: Union[str, object] = values.unset,
        sms_enabled: Union[bool, object] = values.unset,
        mms_enabled: Union[bool, object] = values.unset,
        voice_enabled: Union[bool, object] = values.unset,
        exclude_all_address_required: Union[bool, object] = values.unset,
        exclude_local_address_required: Union[bool, object] = values.unset,
        exclude_foreign_address_required: Union[bool, object] = values.unset,
        beta: Union[bool, object] = values.unset,
        near_number: Union[str, object] = values.unset,
        near_lat_long: Union[str, object] = values.unset,
        distance: Union[int, object] = values.unset,
        in_postal_code: Union[str, object] = values.unset,
        in_region: Union[str, object] = values.unset,
        in_rate_center: Union[str, object] = values.unset,
        in_lata: Union[str, object] = values.unset,
        in_locality: Union[str, object] = values.unset,
        fax_enabled: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> NationalPage:
        """
        Asynchronously retrieve a single page of NationalInstance records from the API.
        Request is executed immediately

        :param area_code: The area code of the phone numbers to read. Applies to only phone numbers in the US and Canada.
        :param contains: The pattern on which to match phone numbers. Valid characters are `*`, `0-9`, `a-z`, and `A-Z`. The `*` character matches any single digit. For examples, see [Example 2](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-2) and [Example 3](https://www.twilio.com/docs/phone-numbers/api/availablephonenumber-resource#local-get-basic-example-3). If specified, this value must have at least two characters.
        :param sms_enabled: Whether the phone numbers can receive text messages. Can be: `true` or `false`.
        :param mms_enabled: Whether the phone numbers can receive MMS messages. Can be: `true` or `false`.
        :param voice_enabled: Whether the phone numbers can receive calls. Can be: `true` or `false`.
        :param exclude_all_address_required: Whether to exclude phone numbers that require an [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param exclude_local_address_required: Whether to exclude phone numbers that require a local [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param exclude_foreign_address_required: Whether to exclude phone numbers that require a foreign [Address](https://www.twilio.com/docs/usage/api/address). Can be: `true` or `false` and the default is `false`.
        :param beta: Whether to read phone numbers that are new to the Twilio platform. Can be: `true` or `false` and the default is `true`.
        :param near_number: Given a phone number, find a geographically close number within `distance` miles. Distance defaults to 25 miles. Applies to only phone numbers in the US and Canada.
        :param near_lat_long: Given a latitude/longitude pair `lat,long` find geographically close numbers within `distance` miles. Applies to only phone numbers in the US and Canada.
        :param distance: The search radius, in miles, for a `near_` query.  Can be up to `500` and the default is `25`. Applies to only phone numbers in the US and Canada.
        :param in_postal_code: Limit results to a particular postal code. Given a phone number, search within the same postal code as that number. Applies to only phone numbers in the US and Canada.
        :param in_region: Limit results to a particular region, state, or province. Given a phone number, search within the same region as that number. Applies to only phone numbers in the US and Canada.
        :param in_rate_center: Limit results to a specific rate center, or given a phone number search within the same rate center as that number. Requires `in_lata` to be set as well. Applies to only phone numbers in the US and Canada.
        :param in_lata: Limit results to a specific local access and transport area ([LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area)). Given a phone number, search within the same [LATA](https://en.wikipedia.org/wiki/Local_access_and_transport_area) as that number. Applies to only phone numbers in the US and Canada.
        :param in_locality: Limit results to a particular locality or city. Given a phone number, search within the same Locality as that number.
        :param fax_enabled: Whether the phone numbers can receive faxes. Can be: `true` or `false`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of NationalInstance
        """
        data = values.of(
            {
                "AreaCode": area_code,
                "Contains": contains,
                "SmsEnabled": serialize.boolean_to_string(sms_enabled),
                "MmsEnabled": serialize.boolean_to_string(mms_enabled),
                "VoiceEnabled": serialize.boolean_to_string(voice_enabled),
                "ExcludeAllAddressRequired": serialize.boolean_to_string(
                    exclude_all_address_required
                ),
                "ExcludeLocalAddressRequired": serialize.boolean_to_string(
                    exclude_local_address_required
                ),
                "ExcludeForeignAddressRequired": serialize.boolean_to_string(
                    exclude_foreign_address_required
                ),
                "Beta": serialize.boolean_to_string(beta),
                "NearNumber": near_number,
                "NearLatLong": near_lat_long,
                "Distance": distance,
                "InPostalCode": in_postal_code,
                "InRegion": in_region,
                "InRateCenter": in_rate_center,
                "InLata": in_lata,
                "InLocality": in_locality,
                "FaxEnabled": serialize.boolean_to_string(fax_enabled),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return NationalPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> NationalPage:
        """
        Retrieve a specific page of NationalInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of NationalInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return NationalPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> NationalPage:
        """
        Asynchronously retrieve a specific page of NationalInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of NationalInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return NationalPage(self._version, response, self._solution)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.NationalList>"
