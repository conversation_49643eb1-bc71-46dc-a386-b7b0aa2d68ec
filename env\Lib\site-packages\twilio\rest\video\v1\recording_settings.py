r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Video
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional, Union
from twilio.base import serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class RecordingSettingsInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the RecordingSettings resource.
    :ivar friendly_name: The string that you assigned to describe the resource and show the user in the console
    :ivar aws_credentials_sid: The SID of the stored Credential resource.
    :ivar aws_s3_url: The URL of the AWS S3 bucket where the recordings are stored. We only support DNS-compliant URLs like `https://documentation-example-twilio-bucket/recordings`, where `recordings` is the path in which you want the recordings to be stored. This URL accepts only URI-valid characters, as described in the [RFC 3986](https://tools.ietf.org/html/rfc3986#section-2).
    :ivar aws_storage_enabled: Whether all recordings are written to the `aws_s3_url`. When `false`, all recordings are stored in our cloud.
    :ivar encryption_key_sid: The SID of the Public Key resource used for encryption.
    :ivar encryption_enabled: Whether all recordings are stored in an encrypted form. The default is `false`.
    :ivar url: The absolute URL of the resource.
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.aws_credentials_sid: Optional[str] = payload.get("aws_credentials_sid")
        self.aws_s3_url: Optional[str] = payload.get("aws_s3_url")
        self.aws_storage_enabled: Optional[bool] = payload.get("aws_storage_enabled")
        self.encryption_key_sid: Optional[str] = payload.get("encryption_key_sid")
        self.encryption_enabled: Optional[bool] = payload.get("encryption_enabled")
        self.url: Optional[str] = payload.get("url")

        self._context: Optional[RecordingSettingsContext] = None

    @property
    def _proxy(self) -> "RecordingSettingsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: RecordingSettingsContext for this RecordingSettingsInstance
        """
        if self._context is None:
            self._context = RecordingSettingsContext(
                self._version,
            )
        return self._context

    def create(
        self,
        friendly_name: str,
        aws_credentials_sid: Union[str, object] = values.unset,
        encryption_key_sid: Union[str, object] = values.unset,
        aws_s3_url: Union[str, object] = values.unset,
        aws_storage_enabled: Union[bool, object] = values.unset,
        encryption_enabled: Union[bool, object] = values.unset,
    ) -> "RecordingSettingsInstance":
        """
        Create the RecordingSettingsInstance

        :param friendly_name: A descriptive string that you create to describe the resource and be shown to users in the console
        :param aws_credentials_sid: The SID of the stored Credential resource.
        :param encryption_key_sid: The SID of the Public Key resource to use for encryption.
        :param aws_s3_url: The URL of the AWS S3 bucket where the recordings should be stored. We only support DNS-compliant URLs like `https://documentation-example-twilio-bucket/recordings`, where `recordings` is the path in which you want the recordings to be stored. This URL accepts only URI-valid characters, as described in the [RFC 3986](https://tools.ietf.org/html/rfc3986#section-2).
        :param aws_storage_enabled: Whether all recordings should be written to the `aws_s3_url`. When `false`, all recordings are stored in our cloud.
        :param encryption_enabled: Whether all recordings should be stored in an encrypted form. The default is `false`.

        :returns: The created RecordingSettingsInstance
        """
        return self._proxy.create(
            friendly_name,
            aws_credentials_sid=aws_credentials_sid,
            encryption_key_sid=encryption_key_sid,
            aws_s3_url=aws_s3_url,
            aws_storage_enabled=aws_storage_enabled,
            encryption_enabled=encryption_enabled,
        )

    async def create_async(
        self,
        friendly_name: str,
        aws_credentials_sid: Union[str, object] = values.unset,
        encryption_key_sid: Union[str, object] = values.unset,
        aws_s3_url: Union[str, object] = values.unset,
        aws_storage_enabled: Union[bool, object] = values.unset,
        encryption_enabled: Union[bool, object] = values.unset,
    ) -> "RecordingSettingsInstance":
        """
        Asynchronous coroutine to create the RecordingSettingsInstance

        :param friendly_name: A descriptive string that you create to describe the resource and be shown to users in the console
        :param aws_credentials_sid: The SID of the stored Credential resource.
        :param encryption_key_sid: The SID of the Public Key resource to use for encryption.
        :param aws_s3_url: The URL of the AWS S3 bucket where the recordings should be stored. We only support DNS-compliant URLs like `https://documentation-example-twilio-bucket/recordings`, where `recordings` is the path in which you want the recordings to be stored. This URL accepts only URI-valid characters, as described in the [RFC 3986](https://tools.ietf.org/html/rfc3986#section-2).
        :param aws_storage_enabled: Whether all recordings should be written to the `aws_s3_url`. When `false`, all recordings are stored in our cloud.
        :param encryption_enabled: Whether all recordings should be stored in an encrypted form. The default is `false`.

        :returns: The created RecordingSettingsInstance
        """
        return await self._proxy.create_async(
            friendly_name,
            aws_credentials_sid=aws_credentials_sid,
            encryption_key_sid=encryption_key_sid,
            aws_s3_url=aws_s3_url,
            aws_storage_enabled=aws_storage_enabled,
            encryption_enabled=encryption_enabled,
        )

    def fetch(self) -> "RecordingSettingsInstance":
        """
        Fetch the RecordingSettingsInstance


        :returns: The fetched RecordingSettingsInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "RecordingSettingsInstance":
        """
        Asynchronous coroutine to fetch the RecordingSettingsInstance


        :returns: The fetched RecordingSettingsInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Video.V1.RecordingSettingsInstance>"


class RecordingSettingsContext(InstanceContext):

    def __init__(self, version: Version):
        """
        Initialize the RecordingSettingsContext

        :param version: Version that contains the resource
        """
        super().__init__(version)

        self._uri = "/RecordingSettings/Default"

    def create(
        self,
        friendly_name: str,
        aws_credentials_sid: Union[str, object] = values.unset,
        encryption_key_sid: Union[str, object] = values.unset,
        aws_s3_url: Union[str, object] = values.unset,
        aws_storage_enabled: Union[bool, object] = values.unset,
        encryption_enabled: Union[bool, object] = values.unset,
    ) -> RecordingSettingsInstance:
        """
        Create the RecordingSettingsInstance

        :param friendly_name: A descriptive string that you create to describe the resource and be shown to users in the console
        :param aws_credentials_sid: The SID of the stored Credential resource.
        :param encryption_key_sid: The SID of the Public Key resource to use for encryption.
        :param aws_s3_url: The URL of the AWS S3 bucket where the recordings should be stored. We only support DNS-compliant URLs like `https://documentation-example-twilio-bucket/recordings`, where `recordings` is the path in which you want the recordings to be stored. This URL accepts only URI-valid characters, as described in the [RFC 3986](https://tools.ietf.org/html/rfc3986#section-2).
        :param aws_storage_enabled: Whether all recordings should be written to the `aws_s3_url`. When `false`, all recordings are stored in our cloud.
        :param encryption_enabled: Whether all recordings should be stored in an encrypted form. The default is `false`.

        :returns: The created RecordingSettingsInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "AwsCredentialsSid": aws_credentials_sid,
                "EncryptionKeySid": encryption_key_sid,
                "AwsS3Url": aws_s3_url,
                "AwsStorageEnabled": serialize.boolean_to_string(aws_storage_enabled),
                "EncryptionEnabled": serialize.boolean_to_string(encryption_enabled),
            }
        )

        payload = self._version.create(method="POST", uri=self._uri, data=data)

        return RecordingSettingsInstance(self._version, payload)

    async def create_async(
        self,
        friendly_name: str,
        aws_credentials_sid: Union[str, object] = values.unset,
        encryption_key_sid: Union[str, object] = values.unset,
        aws_s3_url: Union[str, object] = values.unset,
        aws_storage_enabled: Union[bool, object] = values.unset,
        encryption_enabled: Union[bool, object] = values.unset,
    ) -> RecordingSettingsInstance:
        """
        Asynchronous coroutine to create the RecordingSettingsInstance

        :param friendly_name: A descriptive string that you create to describe the resource and be shown to users in the console
        :param aws_credentials_sid: The SID of the stored Credential resource.
        :param encryption_key_sid: The SID of the Public Key resource to use for encryption.
        :param aws_s3_url: The URL of the AWS S3 bucket where the recordings should be stored. We only support DNS-compliant URLs like `https://documentation-example-twilio-bucket/recordings`, where `recordings` is the path in which you want the recordings to be stored. This URL accepts only URI-valid characters, as described in the [RFC 3986](https://tools.ietf.org/html/rfc3986#section-2).
        :param aws_storage_enabled: Whether all recordings should be written to the `aws_s3_url`. When `false`, all recordings are stored in our cloud.
        :param encryption_enabled: Whether all recordings should be stored in an encrypted form. The default is `false`.

        :returns: The created RecordingSettingsInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "AwsCredentialsSid": aws_credentials_sid,
                "EncryptionKeySid": encryption_key_sid,
                "AwsS3Url": aws_s3_url,
                "AwsStorageEnabled": serialize.boolean_to_string(aws_storage_enabled),
                "EncryptionEnabled": serialize.boolean_to_string(encryption_enabled),
            }
        )

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data
        )

        return RecordingSettingsInstance(self._version, payload)

    def fetch(self) -> RecordingSettingsInstance:
        """
        Fetch the RecordingSettingsInstance


        :returns: The fetched RecordingSettingsInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return RecordingSettingsInstance(
            self._version,
            payload,
        )

    async def fetch_async(self) -> RecordingSettingsInstance:
        """
        Asynchronous coroutine to fetch the RecordingSettingsInstance


        :returns: The fetched RecordingSettingsInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return RecordingSettingsInstance(
            self._version,
            payload,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Video.V1.RecordingSettingsContext>"


class RecordingSettingsList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the RecordingSettingsList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self) -> RecordingSettingsContext:
        """
        Constructs a RecordingSettingsContext

        """
        return RecordingSettingsContext(self._version)

    def __call__(self) -> RecordingSettingsContext:
        """
        Constructs a RecordingSettingsContext

        """
        return RecordingSettingsContext(self._version)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Video.V1.RecordingSettingsList>"
