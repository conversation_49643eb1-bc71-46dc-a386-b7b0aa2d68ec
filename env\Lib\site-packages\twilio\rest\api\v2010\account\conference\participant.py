r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Api
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class ParticipantInstance(InstanceResource):

    class Status(object):
        QUEUED = "queued"
        CONNECTING = "connecting"
        RINGING = "ringing"
        CONNECTED = "connected"
        COMPLETE = "complete"
        FAILED = "failed"

    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Participant resource.
    :ivar call_sid: The SID of the [Call](https://www.twilio.com/docs/voice/api/call-resource) the Participant resource is associated with.
    :ivar label: The user-specified label of this participant, if one was given when the participant was created. This may be used to fetch, update or delete the participant.
    :ivar call_sid_to_coach: The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
    :ivar coaching: Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
    :ivar conference_sid: The SID of the conference the participant is in.
    :ivar date_created: The date and time in GMT that the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT that the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar end_conference_on_exit: Whether the conference ends when the participant leaves. Can be: `true` or `false` and the default is `false`. If `true`, the conference ends and all other participants drop out when the participant leaves.
    :ivar muted: Whether the participant is muted. Can be `true` or `false`.
    :ivar hold: Whether the participant is on hold. Can be `true` or `false`.
    :ivar start_conference_on_enter: Whether the conference starts when the participant joins the conference, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
    :ivar status: 
    :ivar queue_time: The wait time in milliseconds before participant's call is placed. Only available in the response to a create participant request.
    :ivar uri: The URI of the resource, relative to `https://api.twilio.com`.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        account_sid: str,
        conference_sid: str,
        call_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.call_sid: Optional[str] = payload.get("call_sid")
        self.label: Optional[str] = payload.get("label")
        self.call_sid_to_coach: Optional[str] = payload.get("call_sid_to_coach")
        self.coaching: Optional[bool] = payload.get("coaching")
        self.conference_sid: Optional[str] = payload.get("conference_sid")
        self.date_created: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.rfc2822_datetime(
            payload.get("date_updated")
        )
        self.end_conference_on_exit: Optional[bool] = payload.get(
            "end_conference_on_exit"
        )
        self.muted: Optional[bool] = payload.get("muted")
        self.hold: Optional[bool] = payload.get("hold")
        self.start_conference_on_enter: Optional[bool] = payload.get(
            "start_conference_on_enter"
        )
        self.status: Optional["ParticipantInstance.Status"] = payload.get("status")
        self.queue_time: Optional[str] = payload.get("queue_time")
        self.uri: Optional[str] = payload.get("uri")

        self._solution = {
            "account_sid": account_sid,
            "conference_sid": conference_sid,
            "call_sid": call_sid or self.call_sid,
        }
        self._context: Optional[ParticipantContext] = None

    @property
    def _proxy(self) -> "ParticipantContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ParticipantContext for this ParticipantInstance
        """
        if self._context is None:
            self._context = ParticipantContext(
                self._version,
                account_sid=self._solution["account_sid"],
                conference_sid=self._solution["conference_sid"],
                call_sid=self._solution["call_sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ParticipantInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ParticipantInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ParticipantInstance":
        """
        Fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ParticipantInstance":
        """
        Asynchronous coroutine to fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        hold_url: Union[str, object] = values.unset,
        hold_method: Union[str, object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        beep_on_exit: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        call_sid_to_coach: Union[str, object] = values.unset,
    ) -> "ParticipantInstance":
        """
        Update the ParticipantInstance

        :param muted: Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
        :param hold: Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
        :param hold_url: The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param hold_method: The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
        :param announce_url: The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
        :param wait_url: The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param beep_on_exit: Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
        :param end_conference_on_exit: Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
        :param coaching: Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
        :param call_sid_to_coach: The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.

        :returns: The updated ParticipantInstance
        """
        return self._proxy.update(
            muted=muted,
            hold=hold,
            hold_url=hold_url,
            hold_method=hold_method,
            announce_url=announce_url,
            announce_method=announce_method,
            wait_url=wait_url,
            wait_method=wait_method,
            beep_on_exit=beep_on_exit,
            end_conference_on_exit=end_conference_on_exit,
            coaching=coaching,
            call_sid_to_coach=call_sid_to_coach,
        )

    async def update_async(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        hold_url: Union[str, object] = values.unset,
        hold_method: Union[str, object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        beep_on_exit: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        call_sid_to_coach: Union[str, object] = values.unset,
    ) -> "ParticipantInstance":
        """
        Asynchronous coroutine to update the ParticipantInstance

        :param muted: Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
        :param hold: Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
        :param hold_url: The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param hold_method: The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
        :param announce_url: The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
        :param wait_url: The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param beep_on_exit: Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
        :param end_conference_on_exit: Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
        :param coaching: Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
        :param call_sid_to_coach: The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.

        :returns: The updated ParticipantInstance
        """
        return await self._proxy.update_async(
            muted=muted,
            hold=hold,
            hold_url=hold_url,
            hold_method=hold_method,
            announce_url=announce_url,
            announce_method=announce_method,
            wait_url=wait_url,
            wait_method=wait_method,
            beep_on_exit=beep_on_exit,
            end_conference_on_exit=end_conference_on_exit,
            coaching=coaching,
            call_sid_to_coach=call_sid_to_coach,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.ParticipantInstance {}>".format(context)


class ParticipantContext(InstanceContext):

    def __init__(
        self, version: Version, account_sid: str, conference_sid: str, call_sid: str
    ):
        """
        Initialize the ParticipantContext

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Participant resources to update.
        :param conference_sid: The SID of the conference with the participant to update.
        :param call_sid: The [Call](https://www.twilio.com/docs/voice/api/call-resource) SID or label of the participant to update. Non URL safe characters in a label must be percent encoded, for example, a space character is represented as %20.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "conference_sid": conference_sid,
            "call_sid": call_sid,
        }
        self._uri = "/Accounts/{account_sid}/Conferences/{conference_sid}/Participants/{call_sid}.json".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the ParticipantInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ParticipantInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ParticipantInstance:
        """
        Fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ParticipantInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
            call_sid=self._solution["call_sid"],
        )

    async def fetch_async(self) -> ParticipantInstance:
        """
        Asynchronous coroutine to fetch the ParticipantInstance


        :returns: The fetched ParticipantInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
            call_sid=self._solution["call_sid"],
        )

    def update(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        hold_url: Union[str, object] = values.unset,
        hold_method: Union[str, object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        beep_on_exit: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        call_sid_to_coach: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Update the ParticipantInstance

        :param muted: Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
        :param hold: Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
        :param hold_url: The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param hold_method: The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
        :param announce_url: The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
        :param wait_url: The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param beep_on_exit: Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
        :param end_conference_on_exit: Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
        :param coaching: Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
        :param call_sid_to_coach: The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.

        :returns: The updated ParticipantInstance
        """

        data = values.of(
            {
                "Muted": serialize.boolean_to_string(muted),
                "Hold": serialize.boolean_to_string(hold),
                "HoldUrl": hold_url,
                "HoldMethod": hold_method,
                "AnnounceUrl": announce_url,
                "AnnounceMethod": announce_method,
                "WaitUrl": wait_url,
                "WaitMethod": wait_method,
                "BeepOnExit": serialize.boolean_to_string(beep_on_exit),
                "EndConferenceOnExit": serialize.boolean_to_string(
                    end_conference_on_exit
                ),
                "Coaching": serialize.boolean_to_string(coaching),
                "CallSidToCoach": call_sid_to_coach,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
            call_sid=self._solution["call_sid"],
        )

    async def update_async(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        hold_url: Union[str, object] = values.unset,
        hold_method: Union[str, object] = values.unset,
        announce_url: Union[str, object] = values.unset,
        announce_method: Union[str, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        beep_on_exit: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        call_sid_to_coach: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Asynchronous coroutine to update the ParticipantInstance

        :param muted: Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
        :param hold: Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
        :param hold_url: The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param hold_method: The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
        :param announce_url: The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
        :param announce_method: The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
        :param wait_url: The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param beep_on_exit: Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
        :param end_conference_on_exit: Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
        :param coaching: Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
        :param call_sid_to_coach: The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.

        :returns: The updated ParticipantInstance
        """

        data = values.of(
            {
                "Muted": serialize.boolean_to_string(muted),
                "Hold": serialize.boolean_to_string(hold),
                "HoldUrl": hold_url,
                "HoldMethod": hold_method,
                "AnnounceUrl": announce_url,
                "AnnounceMethod": announce_method,
                "WaitUrl": wait_url,
                "WaitMethod": wait_method,
                "BeepOnExit": serialize.boolean_to_string(beep_on_exit),
                "EndConferenceOnExit": serialize.boolean_to_string(
                    end_conference_on_exit
                ),
                "Coaching": serialize.boolean_to_string(coaching),
                "CallSidToCoach": call_sid_to_coach,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
            call_sid=self._solution["call_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Api.V2010.ParticipantContext {}>".format(context)


class ParticipantPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ParticipantInstance:
        """
        Build an instance of ParticipantInstance

        :param payload: Payload response from the API
        """
        return ParticipantInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ParticipantPage>"


class ParticipantList(ListResource):

    def __init__(self, version: Version, account_sid: str, conference_sid: str):
        """
        Initialize the ParticipantList

        :param version: Version that contains the resource
        :param account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Participant resources to read.
        :param conference_sid: The SID of the conference with the participants to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "account_sid": account_sid,
            "conference_sid": conference_sid,
        }
        self._uri = "/Accounts/{account_sid}/Conferences/{conference_sid}/Participants.json".format(
            **self._solution
        )

    def create(
        self,
        from_: str,
        to: str,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        status_callback_event: Union[List[str], object] = values.unset,
        label: Union[str, object] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        muted: Union[bool, object] = values.unset,
        beep: Union[str, object] = values.unset,
        start_conference_on_enter: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        early_media: Union[bool, object] = values.unset,
        max_participants: Union[int, object] = values.unset,
        conference_record: Union[str, object] = values.unset,
        conference_trim: Union[str, object] = values.unset,
        conference_status_callback: Union[str, object] = values.unset,
        conference_status_callback_method: Union[str, object] = values.unset,
        conference_status_callback_event: Union[List[str], object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        region: Union[str, object] = values.unset,
        conference_recording_status_callback: Union[str, object] = values.unset,
        conference_recording_status_callback_method: Union[str, object] = values.unset,
        recording_status_callback_event: Union[List[str], object] = values.unset,
        conference_recording_status_callback_event: Union[
            List[str], object
        ] = values.unset,
        coaching: Union[bool, object] = values.unset,
        call_sid_to_coach: Union[str, object] = values.unset,
        jitter_buffer_size: Union[str, object] = values.unset,
        byoc: Union[str, object] = values.unset,
        caller_id: Union[str, object] = values.unset,
        call_reason: Union[str, object] = values.unset,
        recording_track: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
        machine_detection: Union[str, object] = values.unset,
        machine_detection_timeout: Union[int, object] = values.unset,
        machine_detection_speech_threshold: Union[int, object] = values.unset,
        machine_detection_speech_end_threshold: Union[int, object] = values.unset,
        machine_detection_silence_timeout: Union[int, object] = values.unset,
        amd_status_callback: Union[str, object] = values.unset,
        amd_status_callback_method: Union[str, object] = values.unset,
        trim: Union[str, object] = values.unset,
        call_token: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Create the ParticipantInstance

        :param from_: The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `from` must also be a phone number. If `to` is sip address, this value of `from` should be a username portion to be used to populate the P-Asserted-Identity header that is passed to the SIP endpoint.
        :param to: The phone number, SIP address, or Client identifier that received this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). SIP addresses are formatted as `sip:<EMAIL>`. Client identifiers are formatted `client:name`. [Custom parameters](https://www.twilio.com/docs/voice/api/conference-participant-resource#custom-parameters) may also be specified.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` and `POST` and defaults to `POST`.
        :param status_callback_event: The conference state changes that should generate a call to `status_callback`. Can be: `initiated`, `ringing`, `answered`, and `completed`. Separate multiple values with a space. The default value is `completed`.
        :param label: A label for this participant. If one is supplied, it may subsequently be used to fetch, update or delete the participant.
        :param timeout: The number of seconds that we should allow the phone to ring before assuming there is no answer. Can be an integer between `5` and `600`, inclusive. The default value is `60`. We always add a 5-second timeout buffer to outgoing calls, so  value of 10 would result in an actual timeout that was closer to 15 seconds.
        :param record: Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
        :param muted: Whether the agent is muted in the conference. Can be `true` or `false` and the default is `false`.
        :param beep: Whether to play a notification beep to the conference when the participant joins. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
        :param start_conference_on_enter: Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
        :param end_conference_on_exit: Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
        :param wait_url: The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param early_media: Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. Can be: `true` or `false` and defaults to `true`.
        :param max_participants: The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
        :param conference_record: Whether to record the conference the participant is joining. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
        :param conference_trim: Whether to trim leading and trailing silence from the conference recording. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
        :param conference_status_callback: The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
        :param conference_status_callback_method: The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_status_callback_event: The conference state changes that should generate a call to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `modify`, `speaker`, and `announcement`. Separate multiple values with a space. Defaults to `start end`.
        :param recording_channels: The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
        :param recording_status_callback: The URL that we should call using the `recording_status_callback_method` when the recording status changes.
        :param recording_status_callback_method: The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param sip_auth_username: The SIP username used for authentication.
        :param sip_auth_password: The SIP password for authentication.
        :param region: The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
        :param conference_recording_status_callback: The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
        :param conference_recording_status_callback_method: The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param recording_status_callback_event: The recording state changes that should generate a call to `recording_status_callback`. Can be: `started`, `in-progress`, `paused`, `resumed`, `stopped`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`.
        :param conference_recording_status_callback_event: The conference recording state changes that generate a call to `conference_recording_status_callback`. Can be: `in-progress`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`
        :param coaching: Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
        :param call_sid_to_coach: The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
        :param jitter_buffer_size: Jitter buffer size for the connecting participant. Twilio will use this setting to apply Jitter Buffer before participant's audio is mixed into the conference. Can be: `off`, `small`, `medium`, and `large`. Default to `large`.
        :param byoc: The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
        :param caller_id: The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `callerId` must also be a phone number. If `to` is sip address, this value of `callerId` should be a username portion to be used to populate the From header that is passed to the SIP endpoint.
        :param call_reason: The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
        :param recording_track: The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is sent from Twilio. `both` records the audio that is received and sent by Twilio.
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.
        :param machine_detection: Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
        :param machine_detection_timeout: The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
        :param machine_detection_speech_threshold: The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
        :param machine_detection_speech_end_threshold: The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
        :param machine_detection_silence_timeout: The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
        :param amd_status_callback: The URL that we should call using the `amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
        :param amd_status_callback_method: The HTTP method we should use when calling the `amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
        :param trim: Whether to trim any leading and trailing silence from the participant recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
        :param call_token: A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.

        :returns: The created ParticipantInstance
        """

        data = values.of(
            {
                "From": from_,
                "To": to,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "StatusCallbackEvent": serialize.map(
                    status_callback_event, lambda e: e
                ),
                "Label": label,
                "Timeout": timeout,
                "Record": serialize.boolean_to_string(record),
                "Muted": serialize.boolean_to_string(muted),
                "Beep": beep,
                "StartConferenceOnEnter": serialize.boolean_to_string(
                    start_conference_on_enter
                ),
                "EndConferenceOnExit": serialize.boolean_to_string(
                    end_conference_on_exit
                ),
                "WaitUrl": wait_url,
                "WaitMethod": wait_method,
                "EarlyMedia": serialize.boolean_to_string(early_media),
                "MaxParticipants": max_participants,
                "ConferenceRecord": conference_record,
                "ConferenceTrim": conference_trim,
                "ConferenceStatusCallback": conference_status_callback,
                "ConferenceStatusCallbackMethod": conference_status_callback_method,
                "ConferenceStatusCallbackEvent": serialize.map(
                    conference_status_callback_event, lambda e: e
                ),
                "RecordingChannels": recording_channels,
                "RecordingStatusCallback": recording_status_callback,
                "RecordingStatusCallbackMethod": recording_status_callback_method,
                "SipAuthUsername": sip_auth_username,
                "SipAuthPassword": sip_auth_password,
                "Region": region,
                "ConferenceRecordingStatusCallback": conference_recording_status_callback,
                "ConferenceRecordingStatusCallbackMethod": conference_recording_status_callback_method,
                "RecordingStatusCallbackEvent": serialize.map(
                    recording_status_callback_event, lambda e: e
                ),
                "ConferenceRecordingStatusCallbackEvent": serialize.map(
                    conference_recording_status_callback_event, lambda e: e
                ),
                "Coaching": serialize.boolean_to_string(coaching),
                "CallSidToCoach": call_sid_to_coach,
                "JitterBufferSize": jitter_buffer_size,
                "Byoc": byoc,
                "CallerId": caller_id,
                "CallReason": call_reason,
                "RecordingTrack": recording_track,
                "TimeLimit": time_limit,
                "MachineDetection": machine_detection,
                "MachineDetectionTimeout": machine_detection_timeout,
                "MachineDetectionSpeechThreshold": machine_detection_speech_threshold,
                "MachineDetectionSpeechEndThreshold": machine_detection_speech_end_threshold,
                "MachineDetectionSilenceTimeout": machine_detection_silence_timeout,
                "AmdStatusCallback": amd_status_callback,
                "AmdStatusCallbackMethod": amd_status_callback_method,
                "Trim": trim,
                "CallToken": call_token,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
        )

    async def create_async(
        self,
        from_: str,
        to: str,
        status_callback: Union[str, object] = values.unset,
        status_callback_method: Union[str, object] = values.unset,
        status_callback_event: Union[List[str], object] = values.unset,
        label: Union[str, object] = values.unset,
        timeout: Union[int, object] = values.unset,
        record: Union[bool, object] = values.unset,
        muted: Union[bool, object] = values.unset,
        beep: Union[str, object] = values.unset,
        start_conference_on_enter: Union[bool, object] = values.unset,
        end_conference_on_exit: Union[bool, object] = values.unset,
        wait_url: Union[str, object] = values.unset,
        wait_method: Union[str, object] = values.unset,
        early_media: Union[bool, object] = values.unset,
        max_participants: Union[int, object] = values.unset,
        conference_record: Union[str, object] = values.unset,
        conference_trim: Union[str, object] = values.unset,
        conference_status_callback: Union[str, object] = values.unset,
        conference_status_callback_method: Union[str, object] = values.unset,
        conference_status_callback_event: Union[List[str], object] = values.unset,
        recording_channels: Union[str, object] = values.unset,
        recording_status_callback: Union[str, object] = values.unset,
        recording_status_callback_method: Union[str, object] = values.unset,
        sip_auth_username: Union[str, object] = values.unset,
        sip_auth_password: Union[str, object] = values.unset,
        region: Union[str, object] = values.unset,
        conference_recording_status_callback: Union[str, object] = values.unset,
        conference_recording_status_callback_method: Union[str, object] = values.unset,
        recording_status_callback_event: Union[List[str], object] = values.unset,
        conference_recording_status_callback_event: Union[
            List[str], object
        ] = values.unset,
        coaching: Union[bool, object] = values.unset,
        call_sid_to_coach: Union[str, object] = values.unset,
        jitter_buffer_size: Union[str, object] = values.unset,
        byoc: Union[str, object] = values.unset,
        caller_id: Union[str, object] = values.unset,
        call_reason: Union[str, object] = values.unset,
        recording_track: Union[str, object] = values.unset,
        time_limit: Union[int, object] = values.unset,
        machine_detection: Union[str, object] = values.unset,
        machine_detection_timeout: Union[int, object] = values.unset,
        machine_detection_speech_threshold: Union[int, object] = values.unset,
        machine_detection_speech_end_threshold: Union[int, object] = values.unset,
        machine_detection_silence_timeout: Union[int, object] = values.unset,
        amd_status_callback: Union[str, object] = values.unset,
        amd_status_callback_method: Union[str, object] = values.unset,
        trim: Union[str, object] = values.unset,
        call_token: Union[str, object] = values.unset,
    ) -> ParticipantInstance:
        """
        Asynchronously create the ParticipantInstance

        :param from_: The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `from` must also be a phone number. If `to` is sip address, this value of `from` should be a username portion to be used to populate the P-Asserted-Identity header that is passed to the SIP endpoint.
        :param to: The phone number, SIP address, or Client identifier that received this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). SIP addresses are formatted as `sip:<EMAIL>`. Client identifiers are formatted `client:name`. [Custom parameters](https://www.twilio.com/docs/voice/api/conference-participant-resource#custom-parameters) may also be specified.
        :param status_callback: The URL we should call using the `status_callback_method` to send status information to your application.
        :param status_callback_method: The HTTP method we should use to call `status_callback`. Can be: `GET` and `POST` and defaults to `POST`.
        :param status_callback_event: The conference state changes that should generate a call to `status_callback`. Can be: `initiated`, `ringing`, `answered`, and `completed`. Separate multiple values with a space. The default value is `completed`.
        :param label: A label for this participant. If one is supplied, it may subsequently be used to fetch, update or delete the participant.
        :param timeout: The number of seconds that we should allow the phone to ring before assuming there is no answer. Can be an integer between `5` and `600`, inclusive. The default value is `60`. We always add a 5-second timeout buffer to outgoing calls, so  value of 10 would result in an actual timeout that was closer to 15 seconds.
        :param record: Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
        :param muted: Whether the agent is muted in the conference. Can be `true` or `false` and the default is `false`.
        :param beep: Whether to play a notification beep to the conference when the participant joins. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
        :param start_conference_on_enter: Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
        :param end_conference_on_exit: Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
        :param wait_url: The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
        :param wait_method: The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
        :param early_media: Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. Can be: `true` or `false` and defaults to `true`.
        :param max_participants: The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
        :param conference_record: Whether to record the conference the participant is joining. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
        :param conference_trim: Whether to trim leading and trailing silence from the conference recording. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
        :param conference_status_callback: The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
        :param conference_status_callback_method: The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param conference_status_callback_event: The conference state changes that should generate a call to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `modify`, `speaker`, and `announcement`. Separate multiple values with a space. Defaults to `start end`.
        :param recording_channels: The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
        :param recording_status_callback: The URL that we should call using the `recording_status_callback_method` when the recording status changes.
        :param recording_status_callback_method: The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param sip_auth_username: The SIP username used for authentication.
        :param sip_auth_password: The SIP password for authentication.
        :param region: The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
        :param conference_recording_status_callback: The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
        :param conference_recording_status_callback_method: The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
        :param recording_status_callback_event: The recording state changes that should generate a call to `recording_status_callback`. Can be: `started`, `in-progress`, `paused`, `resumed`, `stopped`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`.
        :param conference_recording_status_callback_event: The conference recording state changes that generate a call to `conference_recording_status_callback`. Can be: `in-progress`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`
        :param coaching: Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
        :param call_sid_to_coach: The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
        :param jitter_buffer_size: Jitter buffer size for the connecting participant. Twilio will use this setting to apply Jitter Buffer before participant's audio is mixed into the conference. Can be: `off`, `small`, `medium`, and `large`. Default to `large`.
        :param byoc: The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
        :param caller_id: The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `callerId` must also be a phone number. If `to` is sip address, this value of `callerId` should be a username portion to be used to populate the From header that is passed to the SIP endpoint.
        :param call_reason: The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
        :param recording_track: The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is sent from Twilio. `both` records the audio that is received and sent by Twilio.
        :param time_limit: The maximum duration of the call in seconds. Constraints depend on account and configuration.
        :param machine_detection: Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
        :param machine_detection_timeout: The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
        :param machine_detection_speech_threshold: The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
        :param machine_detection_speech_end_threshold: The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
        :param machine_detection_silence_timeout: The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
        :param amd_status_callback: The URL that we should call using the `amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
        :param amd_status_callback_method: The HTTP method we should use when calling the `amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
        :param trim: Whether to trim any leading and trailing silence from the participant recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
        :param call_token: A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.

        :returns: The created ParticipantInstance
        """

        data = values.of(
            {
                "From": from_,
                "To": to,
                "StatusCallback": status_callback,
                "StatusCallbackMethod": status_callback_method,
                "StatusCallbackEvent": serialize.map(
                    status_callback_event, lambda e: e
                ),
                "Label": label,
                "Timeout": timeout,
                "Record": serialize.boolean_to_string(record),
                "Muted": serialize.boolean_to_string(muted),
                "Beep": beep,
                "StartConferenceOnEnter": serialize.boolean_to_string(
                    start_conference_on_enter
                ),
                "EndConferenceOnExit": serialize.boolean_to_string(
                    end_conference_on_exit
                ),
                "WaitUrl": wait_url,
                "WaitMethod": wait_method,
                "EarlyMedia": serialize.boolean_to_string(early_media),
                "MaxParticipants": max_participants,
                "ConferenceRecord": conference_record,
                "ConferenceTrim": conference_trim,
                "ConferenceStatusCallback": conference_status_callback,
                "ConferenceStatusCallbackMethod": conference_status_callback_method,
                "ConferenceStatusCallbackEvent": serialize.map(
                    conference_status_callback_event, lambda e: e
                ),
                "RecordingChannels": recording_channels,
                "RecordingStatusCallback": recording_status_callback,
                "RecordingStatusCallbackMethod": recording_status_callback_method,
                "SipAuthUsername": sip_auth_username,
                "SipAuthPassword": sip_auth_password,
                "Region": region,
                "ConferenceRecordingStatusCallback": conference_recording_status_callback,
                "ConferenceRecordingStatusCallbackMethod": conference_recording_status_callback_method,
                "RecordingStatusCallbackEvent": serialize.map(
                    recording_status_callback_event, lambda e: e
                ),
                "ConferenceRecordingStatusCallbackEvent": serialize.map(
                    conference_recording_status_callback_event, lambda e: e
                ),
                "Coaching": serialize.boolean_to_string(coaching),
                "CallSidToCoach": call_sid_to_coach,
                "JitterBufferSize": jitter_buffer_size,
                "Byoc": byoc,
                "CallerId": caller_id,
                "CallReason": call_reason,
                "RecordingTrack": recording_track,
                "TimeLimit": time_limit,
                "MachineDetection": machine_detection,
                "MachineDetectionTimeout": machine_detection_timeout,
                "MachineDetectionSpeechThreshold": machine_detection_speech_threshold,
                "MachineDetectionSpeechEndThreshold": machine_detection_speech_end_threshold,
                "MachineDetectionSilenceTimeout": machine_detection_silence_timeout,
                "AmdStatusCallback": amd_status_callback,
                "AmdStatusCallbackMethod": amd_status_callback_method,
                "Trim": trim,
                "CallToken": call_token,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ParticipantInstance(
            self._version,
            payload,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
        )

    def stream(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ParticipantInstance]:
        """
        Streams ParticipantInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param bool muted: Whether to return only participants that are muted. Can be: `true` or `false`.
        :param bool hold: Whether to return only participants that are on hold. Can be: `true` or `false`.
        :param bool coaching: Whether to return only participants who are coaching another call. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            muted=muted, hold=hold, coaching=coaching, page_size=limits["page_size"]
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ParticipantInstance]:
        """
        Asynchronously streams ParticipantInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param bool muted: Whether to return only participants that are muted. Can be: `true` or `false`.
        :param bool hold: Whether to return only participants that are on hold. Can be: `true` or `false`.
        :param bool coaching: Whether to return only participants who are coaching another call. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            muted=muted, hold=hold, coaching=coaching, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ParticipantInstance]:
        """
        Lists ParticipantInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param bool muted: Whether to return only participants that are muted. Can be: `true` or `false`.
        :param bool hold: Whether to return only participants that are on hold. Can be: `true` or `false`.
        :param bool coaching: Whether to return only participants who are coaching another call. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                muted=muted,
                hold=hold,
                coaching=coaching,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ParticipantInstance]:
        """
        Asynchronously lists ParticipantInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param bool muted: Whether to return only participants that are muted. Can be: `true` or `false`.
        :param bool hold: Whether to return only participants that are on hold. Can be: `true` or `false`.
        :param bool coaching: Whether to return only participants who are coaching another call. Can be: `true` or `false`.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                muted=muted,
                hold=hold,
                coaching=coaching,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ParticipantPage:
        """
        Retrieve a single page of ParticipantInstance records from the API.
        Request is executed immediately

        :param muted: Whether to return only participants that are muted. Can be: `true` or `false`.
        :param hold: Whether to return only participants that are on hold. Can be: `true` or `false`.
        :param coaching: Whether to return only participants who are coaching another call. Can be: `true` or `false`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ParticipantInstance
        """
        data = values.of(
            {
                "Muted": serialize.boolean_to_string(muted),
                "Hold": serialize.boolean_to_string(hold),
                "Coaching": serialize.boolean_to_string(coaching),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ParticipantPage(self._version, response, self._solution)

    async def page_async(
        self,
        muted: Union[bool, object] = values.unset,
        hold: Union[bool, object] = values.unset,
        coaching: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ParticipantPage:
        """
        Asynchronously retrieve a single page of ParticipantInstance records from the API.
        Request is executed immediately

        :param muted: Whether to return only participants that are muted. Can be: `true` or `false`.
        :param hold: Whether to return only participants that are on hold. Can be: `true` or `false`.
        :param coaching: Whether to return only participants who are coaching another call. Can be: `true` or `false`.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ParticipantInstance
        """
        data = values.of(
            {
                "Muted": serialize.boolean_to_string(muted),
                "Hold": serialize.boolean_to_string(hold),
                "Coaching": serialize.boolean_to_string(coaching),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ParticipantPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> ParticipantPage:
        """
        Retrieve a specific page of ParticipantInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ParticipantInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ParticipantPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> ParticipantPage:
        """
        Asynchronously retrieve a specific page of ParticipantInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ParticipantInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ParticipantPage(self._version, response, self._solution)

    def get(self, call_sid: str) -> ParticipantContext:
        """
        Constructs a ParticipantContext

        :param call_sid: The [Call](https://www.twilio.com/docs/voice/api/call-resource) SID or label of the participant to update. Non URL safe characters in a label must be percent encoded, for example, a space character is represented as %20.
        """
        return ParticipantContext(
            self._version,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
            call_sid=call_sid,
        )

    def __call__(self, call_sid: str) -> ParticipantContext:
        """
        Constructs a ParticipantContext

        :param call_sid: The [Call](https://www.twilio.com/docs/voice/api/call-resource) SID or label of the participant to update. Non URL safe characters in a label must be percent encoded, for example, a space character is represented as %20.
        """
        return ParticipantContext(
            self._version,
            account_sid=self._solution["account_sid"],
            conference_sid=self._solution["conference_sid"],
            call_sid=call_sid,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Api.V2010.ParticipantList>"
