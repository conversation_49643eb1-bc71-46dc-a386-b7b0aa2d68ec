r"""
  This code was generated by
  ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
   |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
   |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

  NOTE: This class is auto generated by OpenAPI Generator.
  https://openapi-generator.tech
  Do not edit the class manually.
"""

from typing import Optional

from twilio.base.domain import Domain
from twilio.rest import Client
from twilio.rest.preview.hosted_numbers import HostedNumbers
from twilio.rest.preview.marketplace import Marketplace
from twilio.rest.preview.wireless import Wireless


class PreviewBase(Domain):

    def __init__(self, twilio: Client):
        """
        Initialize the Preview Domain

        :returns: Domain for Preview
        """
        super().__init__(twilio, "https://preview.twilio.com")
        self._hosted_numbers: Optional[HostedNumbers] = None
        self._marketplace: Optional[Marketplace] = None
        self._wireless: Optional[Wireless] = None

    @property
    def hosted_numbers(self) -> HostedNumbers:
        """
        :returns: Versions hosted_numbers of Preview
        """
        if self._hosted_numbers is None:
            self._hosted_numbers = HostedNumbers(self)
        return self._hosted_numbers

    @property
    def marketplace(self) -> Marketplace:
        """
        :returns: Versions marketplace of Preview
        """
        if self._marketplace is None:
            self._marketplace = Marketplace(self)
        return self._marketplace

    @property
    def wireless(self) -> Wireless:
        """
        :returns: Versions wireless of Preview
        """
        if self._wireless is None:
            self._wireless = Wireless(self)
        return self._wireless

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Preview>"
