r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Events
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class SubscribedEventInstance(InstanceResource):
    """
    :ivar account_sid: The unique SID identifier of the Account.
    :ivar type: Type of event being subscribed to.
    :ivar schema_version: The schema version that the Subscription should use.
    :ivar subscription_sid: The unique SID identifier of the Subscription.
    :ivar url: The URL of this resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        subscription_sid: str,
        type: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.type: Optional[str] = payload.get("type")
        self.schema_version: Optional[int] = deserialize.integer(
            payload.get("schema_version")
        )
        self.subscription_sid: Optional[str] = payload.get("subscription_sid")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "subscription_sid": subscription_sid,
            "type": type or self.type,
        }
        self._context: Optional[SubscribedEventContext] = None

    @property
    def _proxy(self) -> "SubscribedEventContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: SubscribedEventContext for this SubscribedEventInstance
        """
        if self._context is None:
            self._context = SubscribedEventContext(
                self._version,
                subscription_sid=self._solution["subscription_sid"],
                type=self._solution["type"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the SubscribedEventInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the SubscribedEventInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "SubscribedEventInstance":
        """
        Fetch the SubscribedEventInstance


        :returns: The fetched SubscribedEventInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "SubscribedEventInstance":
        """
        Asynchronous coroutine to fetch the SubscribedEventInstance


        :returns: The fetched SubscribedEventInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self, schema_version: Union[int, object] = values.unset
    ) -> "SubscribedEventInstance":
        """
        Update the SubscribedEventInstance

        :param schema_version: The schema version that the Subscription should use.

        :returns: The updated SubscribedEventInstance
        """
        return self._proxy.update(
            schema_version=schema_version,
        )

    async def update_async(
        self, schema_version: Union[int, object] = values.unset
    ) -> "SubscribedEventInstance":
        """
        Asynchronous coroutine to update the SubscribedEventInstance

        :param schema_version: The schema version that the Subscription should use.

        :returns: The updated SubscribedEventInstance
        """
        return await self._proxy.update_async(
            schema_version=schema_version,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Events.V1.SubscribedEventInstance {}>".format(context)


class SubscribedEventContext(InstanceContext):

    def __init__(self, version: Version, subscription_sid: str, type: str):
        """
        Initialize the SubscribedEventContext

        :param version: Version that contains the resource
        :param subscription_sid: The unique SID identifier of the Subscription.
        :param type: Type of event being subscribed to.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "subscription_sid": subscription_sid,
            "type": type,
        }
        self._uri = "/Subscriptions/{subscription_sid}/SubscribedEvents/{type}".format(
            **self._solution
        )

    def delete(self) -> bool:
        """
        Deletes the SubscribedEventInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the SubscribedEventInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> SubscribedEventInstance:
        """
        Fetch the SubscribedEventInstance


        :returns: The fetched SubscribedEventInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return SubscribedEventInstance(
            self._version,
            payload,
            subscription_sid=self._solution["subscription_sid"],
            type=self._solution["type"],
        )

    async def fetch_async(self) -> SubscribedEventInstance:
        """
        Asynchronous coroutine to fetch the SubscribedEventInstance


        :returns: The fetched SubscribedEventInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return SubscribedEventInstance(
            self._version,
            payload,
            subscription_sid=self._solution["subscription_sid"],
            type=self._solution["type"],
        )

    def update(
        self, schema_version: Union[int, object] = values.unset
    ) -> SubscribedEventInstance:
        """
        Update the SubscribedEventInstance

        :param schema_version: The schema version that the Subscription should use.

        :returns: The updated SubscribedEventInstance
        """

        data = values.of(
            {
                "SchemaVersion": schema_version,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return SubscribedEventInstance(
            self._version,
            payload,
            subscription_sid=self._solution["subscription_sid"],
            type=self._solution["type"],
        )

    async def update_async(
        self, schema_version: Union[int, object] = values.unset
    ) -> SubscribedEventInstance:
        """
        Asynchronous coroutine to update the SubscribedEventInstance

        :param schema_version: The schema version that the Subscription should use.

        :returns: The updated SubscribedEventInstance
        """

        data = values.of(
            {
                "SchemaVersion": schema_version,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return SubscribedEventInstance(
            self._version,
            payload,
            subscription_sid=self._solution["subscription_sid"],
            type=self._solution["type"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Events.V1.SubscribedEventContext {}>".format(context)


class SubscribedEventPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> SubscribedEventInstance:
        """
        Build an instance of SubscribedEventInstance

        :param payload: Payload response from the API
        """
        return SubscribedEventInstance(
            self._version, payload, subscription_sid=self._solution["subscription_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Events.V1.SubscribedEventPage>"


class SubscribedEventList(ListResource):

    def __init__(self, version: Version, subscription_sid: str):
        """
        Initialize the SubscribedEventList

        :param version: Version that contains the resource
        :param subscription_sid: The unique SID identifier of the Subscription.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "subscription_sid": subscription_sid,
        }
        self._uri = "/Subscriptions/{subscription_sid}/SubscribedEvents".format(
            **self._solution
        )

    def create(
        self, type: str, schema_version: Union[int, object] = values.unset
    ) -> SubscribedEventInstance:
        """
        Create the SubscribedEventInstance

        :param type: Type of event being subscribed to.
        :param schema_version: The schema version that the Subscription should use.

        :returns: The created SubscribedEventInstance
        """

        data = values.of(
            {
                "Type": type,
                "SchemaVersion": schema_version,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return SubscribedEventInstance(
            self._version, payload, subscription_sid=self._solution["subscription_sid"]
        )

    async def create_async(
        self, type: str, schema_version: Union[int, object] = values.unset
    ) -> SubscribedEventInstance:
        """
        Asynchronously create the SubscribedEventInstance

        :param type: Type of event being subscribed to.
        :param schema_version: The schema version that the Subscription should use.

        :returns: The created SubscribedEventInstance
        """

        data = values.of(
            {
                "Type": type,
                "SchemaVersion": schema_version,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return SubscribedEventInstance(
            self._version, payload, subscription_sid=self._solution["subscription_sid"]
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[SubscribedEventInstance]:
        """
        Streams SubscribedEventInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[SubscribedEventInstance]:
        """
        Asynchronously streams SubscribedEventInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[SubscribedEventInstance]:
        """
        Lists SubscribedEventInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[SubscribedEventInstance]:
        """
        Asynchronously lists SubscribedEventInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> SubscribedEventPage:
        """
        Retrieve a single page of SubscribedEventInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of SubscribedEventInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return SubscribedEventPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> SubscribedEventPage:
        """
        Asynchronously retrieve a single page of SubscribedEventInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of SubscribedEventInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return SubscribedEventPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> SubscribedEventPage:
        """
        Retrieve a specific page of SubscribedEventInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of SubscribedEventInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return SubscribedEventPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> SubscribedEventPage:
        """
        Asynchronously retrieve a specific page of SubscribedEventInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of SubscribedEventInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return SubscribedEventPage(self._version, response, self._solution)

    def get(self, type: str) -> SubscribedEventContext:
        """
        Constructs a SubscribedEventContext

        :param type: Type of event being subscribed to.
        """
        return SubscribedEventContext(
            self._version,
            subscription_sid=self._solution["subscription_sid"],
            type=type,
        )

    def __call__(self, type: str) -> SubscribedEventContext:
        """
        Constructs a SubscribedEventContext

        :param type: Type of event being subscribed to.
        """
        return SubscribedEventContext(
            self._version,
            subscription_sid=self._solution["subscription_sid"],
            type=type,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Events.V1.SubscribedEventList>"
