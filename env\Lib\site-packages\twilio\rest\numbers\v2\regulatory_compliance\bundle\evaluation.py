r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class EvaluationInstance(InstanceResource):

    class Status(object):
        COMPLIANT = "compliant"
        NONCOMPLIANT = "noncompliant"

    """
    :ivar sid: The unique string that identifies the Evaluation resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Bundle resource.
    :ivar regulation_sid: The unique string of a regulation that is associated to the Bundle resource.
    :ivar bundle_sid: The unique string that we created to identify the Bundle resource.
    :ivar status: 
    :ivar results: The results of the Evaluation which includes the valid and invalid attributes.
    :ivar date_created: 
    :ivar url: 
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        bundle_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.regulation_sid: Optional[str] = payload.get("regulation_sid")
        self.bundle_sid: Optional[str] = payload.get("bundle_sid")
        self.status: Optional["EvaluationInstance.Status"] = payload.get("status")
        self.results: Optional[List[Dict[str, object]]] = payload.get("results")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "bundle_sid": bundle_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[EvaluationContext] = None

    @property
    def _proxy(self) -> "EvaluationContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: EvaluationContext for this EvaluationInstance
        """
        if self._context is None:
            self._context = EvaluationContext(
                self._version,
                bundle_sid=self._solution["bundle_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def fetch(self) -> "EvaluationInstance":
        """
        Fetch the EvaluationInstance


        :returns: The fetched EvaluationInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "EvaluationInstance":
        """
        Asynchronous coroutine to fetch the EvaluationInstance


        :returns: The fetched EvaluationInstance
        """
        return await self._proxy.fetch_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.EvaluationInstance {}>".format(context)


class EvaluationContext(InstanceContext):

    def __init__(self, version: Version, bundle_sid: str, sid: str):
        """
        Initialize the EvaluationContext

        :param version: Version that contains the resource
        :param bundle_sid: The unique string that we created to identify the Bundle resource.
        :param sid: The unique string that identifies the Evaluation resource.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "bundle_sid": bundle_sid,
            "sid": sid,
        }
        self._uri = (
            "/RegulatoryCompliance/Bundles/{bundle_sid}/Evaluations/{sid}".format(
                **self._solution
            )
        )

    def fetch(self) -> EvaluationInstance:
        """
        Fetch the EvaluationInstance


        :returns: The fetched EvaluationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return EvaluationInstance(
            self._version,
            payload,
            bundle_sid=self._solution["bundle_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> EvaluationInstance:
        """
        Asynchronous coroutine to fetch the EvaluationInstance


        :returns: The fetched EvaluationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return EvaluationInstance(
            self._version,
            payload,
            bundle_sid=self._solution["bundle_sid"],
            sid=self._solution["sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Numbers.V2.EvaluationContext {}>".format(context)


class EvaluationPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> EvaluationInstance:
        """
        Build an instance of EvaluationInstance

        :param payload: Payload response from the API
        """
        return EvaluationInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2.EvaluationPage>"


class EvaluationList(ListResource):

    def __init__(self, version: Version, bundle_sid: str):
        """
        Initialize the EvaluationList

        :param version: Version that contains the resource
        :param bundle_sid: The unique string that identifies the Bundle resource.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "bundle_sid": bundle_sid,
        }
        self._uri = "/RegulatoryCompliance/Bundles/{bundle_sid}/Evaluations".format(
            **self._solution
        )

    def create(self) -> EvaluationInstance:
        """
        Create the EvaluationInstance


        :returns: The created EvaluationInstance
        """

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = self._version.create(method="POST", uri=self._uri, headers=headers)

        return EvaluationInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    async def create_async(self) -> EvaluationInstance:
        """
        Asynchronously create the EvaluationInstance


        :returns: The created EvaluationInstance
        """

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, headers=headers
        )

        return EvaluationInstance(
            self._version, payload, bundle_sid=self._solution["bundle_sid"]
        )

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[EvaluationInstance]:
        """
        Streams EvaluationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[EvaluationInstance]:
        """
        Asynchronously streams EvaluationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[EvaluationInstance]:
        """
        Lists EvaluationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[EvaluationInstance]:
        """
        Asynchronously lists EvaluationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> EvaluationPage:
        """
        Retrieve a single page of EvaluationInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of EvaluationInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return EvaluationPage(self._version, response, self._solution)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> EvaluationPage:
        """
        Asynchronously retrieve a single page of EvaluationInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of EvaluationInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return EvaluationPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> EvaluationPage:
        """
        Retrieve a specific page of EvaluationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of EvaluationInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return EvaluationPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> EvaluationPage:
        """
        Asynchronously retrieve a specific page of EvaluationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of EvaluationInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return EvaluationPage(self._version, response, self._solution)

    def get(self, sid: str) -> EvaluationContext:
        """
        Constructs a EvaluationContext

        :param sid: The unique string that identifies the Evaluation resource.
        """
        return EvaluationContext(
            self._version, bundle_sid=self._solution["bundle_sid"], sid=sid
        )

    def __call__(self, sid: str) -> EvaluationContext:
        """
        Constructs a EvaluationContext

        :param sid: The unique string that identifies the Evaluation resource.
        """
        return EvaluationContext(
            self._version, bundle_sid=self._solution["bundle_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2.EvaluationList>"
